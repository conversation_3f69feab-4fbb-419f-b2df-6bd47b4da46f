#!/bin/bash

# PM2 Management Script for Khobra Kitchens
# سكريبت إدارة PM2 لخبرة المطابخ

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    echo "استخدام: $0 [الأمر]"
    echo ""
    echo "الأوامر المتاحة:"
    echo "  start       - تشغيل جميع الخدمات"
    echo "  stop        - إيقاف جميع الخدمات"
    echo "  restart     - إعادة تشغيل جميع الخدمات"
    echo "  status      - عرض حالة الخدمات"
    echo "  logs        - عرض السجلات"
    echo "  logs-api    - عرض سجلات API فقط"
    echo "  logs-web    - عرض سجلات الواجهة فقط"
    echo "  reload      - إعادة تحميل الإعدادات"
    echo "  save        - حفظ الحالة الحالية"
    echo "  monit       - مراقبة الأداء"
    echo "  cleanup     - تنظيف العمليات المعلقة"
    echo "  stop-site   - إيقاف الموقع كاملاً (nginx + PM2)"
    echo "  start-site  - تشغيل الموقع كاملاً (nginx + PM2)"
    echo "  maintenance - تفعيل وضع الصيانة"
    echo "  help        - عرض هذه المساعدة"
}

start_services() {
    print_status "تشغيل خدمات خبرة المطابخ..."
    pm2 start ecosystem.config.cjs --env production
    if [ $? -eq 0 ]; then
        print_success "تم تشغيل جميع الخدمات بنجاح"
    else
        print_error "فشل في تشغيل الخدمات"
        exit 1
    fi
}

stop_services() {
    print_status "إيقاف خدمات خبرة المطابخ..."

    # إيقاف عمليات PM2
    pm2 stop all

    # البحث عن عمليات Node.js التي تعمل على البورتات المحددة وإيقافها
    print_status "البحث عن عمليات إضافية على البورتات 3002, 4173, 5173..."

    # إيقاف العمليات على البورت 3002 (API)
    API_PID=$(lsof -ti:3002)
    if [ ! -z "$API_PID" ]; then
        print_warning "إيقاف عملية على البورت 3002 (PID: $API_PID)"
        kill -9 $API_PID 2>/dev/null
    fi

    # إيقاف العمليات على البورت 4173 (Frontend Build)
    FRONTEND_BUILD_PID=$(lsof -ti:4173)
    if [ ! -z "$FRONTEND_BUILD_PID" ]; then
        print_warning "إيقاف عملية على البورت 4173 (PID: $FRONTEND_BUILD_PID)"
        kill -9 $FRONTEND_BUILD_PID 2>/dev/null
    fi

    # إيقاف العمليات على البورت 5173 (Vite Dev Server)
    VITE_PID=$(lsof -ti:5173)
    if [ ! -z "$VITE_PID" ]; then
        print_warning "إيقاف خادم Vite التطويري على البورت 5173 (PID: $VITE_PID)"
        kill -9 $VITE_PID 2>/dev/null
    fi

    # التحقق من حالة البورتات
    sleep 2
    REMAINING_PROCESSES=$(netstat -tulpn 2>/dev/null | grep -E ":(3002|4173|5173)" | wc -l)

    if [ $REMAINING_PROCESSES -eq 0 ]; then
        print_success "تم إيقاف جميع الخدمات بنجاح"
    else
        print_warning "لا تزال هناك بعض العمليات تعمل على البورتات المحددة"
        netstat -tulpn 2>/dev/null | grep -E ":(3002|4173|5173)"
    fi
}

restart_services() {
    print_status "إعادة تشغيل خدمات خبرة المطابخ..."
    pm2 restart all
    if [ $? -eq 0 ]; then
        print_success "تم إعادة تشغيل جميع الخدمات بنجاح"
    else
        print_error "فشل في إعادة تشغيل الخدمات"
        exit 1
    fi
}

show_status() {
    print_status "حالة خدمات خبرة المطابخ:"
    pm2 list
}

show_logs() {
    print_status "عرض سجلات جميع الخدمات:"
    pm2 logs
}

show_api_logs() {
    print_status "عرض سجلات API:"
    pm2 logs khobra-api
}

show_web_logs() {
    print_status "عرض سجلات الواجهة:"
    pm2 logs khobra-frontend
}

reload_services() {
    print_status "إعادة تحميل إعدادات خدمات خبرة المطابخ..."
    pm2 reload ecosystem.config.cjs --env production
    if [ $? -eq 0 ]; then
        print_success "تم إعادة تحميل الإعدادات بنجاح"
    else
        print_error "فشل في إعادة تحميل الإعدادات"
        exit 1
    fi
}

save_state() {
    print_status "حفظ حالة PM2..."
    pm2 save
    if [ $? -eq 0 ]; then
        print_success "تم حفظ الحالة بنجاح"
    else
        print_error "فشل في حفظ الحالة"
        exit 1
    fi
}

monitor_services() {
    print_status "مراقبة أداء الخدمات:"
    pm2 monit
}

cleanup_processes() {
    print_status "تنظيف العمليات المعلقة..."

    # حذف جميع عمليات PM2
    pm2 delete all 2>/dev/null

    # البحث عن جميع عمليات Node.js المتعلقة بالمشروع
    print_status "البحث عن عمليات Node.js المعلقة..."

    # إيقاف عمليات Vite
    VITE_PIDS=$(ps aux | grep "vite" | grep -v grep | awk '{print $2}')
    if [ ! -z "$VITE_PIDS" ]; then
        print_warning "إيقاف عمليات Vite المعلقة..."
        echo "$VITE_PIDS" | xargs kill -9 2>/dev/null
    fi

    # إيقاف عمليات server.js
    SERVER_PIDS=$(ps aux | grep "server.js" | grep -v grep | awk '{print $2}')
    if [ ! -z "$SERVER_PIDS" ]; then
        print_warning "إيقاف عمليات server.js المعلقة..."
        echo "$SERVER_PIDS" | xargs kill -9 2>/dev/null
    fi

    # التحقق النهائي من البورتات
    sleep 2
    REMAINING=$(netstat -tulpn 2>/dev/null | grep -E ":(3002|4173|5173)")
    if [ -z "$REMAINING" ]; then
        print_success "تم تنظيف جميع العمليات المعلقة بنجاح"
    else
        print_warning "لا تزال هناك عمليات تعمل:"
        echo "$REMAINING"
    fi
}

stop_complete_site() {
    print_status "إيقاف الموقع كاملاً (nginx + PM2)..."

    # إيقاف PM2 أولاً
    stop_services

    # إنشاء صفحة صيانة مؤقتة
    create_maintenance_page

    # إعادة تسمية مجلد dist لمنع nginx من خدمة الملفات
    if [ -d "dist" ]; then
        print_status "إخفاء ملفات الموقع..."
        mv dist dist_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null
        mkdir -p dist
        cp maintenance.html dist/index.html 2>/dev/null
    fi

    # إعادة تحميل nginx
    print_status "إعادة تحميل nginx..."
    nginx -s reload 2>/dev/null || systemctl reload nginx 2>/dev/null

    print_success "تم إيقاف الموقع كاملاً - الآن يظهر في وضع الصيانة"
}

start_complete_site() {
    print_status "تشغيل الموقع كاملاً..."

    # البحث عن آخر نسخة احتياطية من dist
    LATEST_BACKUP=$(ls -1t dist_backup_* 2>/dev/null | head -1)

    if [ ! -z "$LATEST_BACKUP" ] && [ -d "$LATEST_BACKUP" ]; then
        print_status "استعادة ملفات الموقع من $LATEST_BACKUP..."
        rm -rf dist 2>/dev/null
        mv "$LATEST_BACKUP" dist
    elif [ ! -d "dist" ] || [ ! -f "dist/index.html" ] || grep -q "صيانة" dist/index.html 2>/dev/null; then
        print_warning "لا توجد ملفات موقع صالحة، يجب بناء الموقع أولاً..."
        print_status "بناء الموقع..."
        npm run build
    fi

    # تشغيل PM2
    start_services

    # إعادة تحميل nginx
    print_status "إعادة تحميل nginx..."
    nginx -s reload 2>/dev/null || systemctl reload nginx 2>/dev/null

    print_success "تم تشغيل الموقع كاملاً بنجاح"
}

create_maintenance_page() {
    print_status "إنشاء صفحة الصيانة..."
    cat > maintenance.html << 'EOF'
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عجائب الخبراء - تحت الصيانة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🔧</div>
        <h1>عجائب الخبراء</h1>
        <p>الموقع تحت الصيانة حالياً<br>سنعود قريباً بتحديثات رائعة!</p>
        <div class="spinner"></div>
    </div>
</body>
</html>
EOF
    print_success "تم إنشاء صفحة الصيانة"
}

enable_maintenance_mode() {
    print_status "تفعيل وضع الصيانة..."

    # إنشاء صفحة الصيانة
    create_maintenance_page

    # نسخ احتياطية من dist إذا لم تكن موجودة
    if [ -d "dist" ] && [ ! -d "dist_maintenance_backup" ]; then
        print_status "إنشاء نسخة احتياطية من الموقع..."
        cp -r dist dist_maintenance_backup
    fi

    # استبدال index.html بصفحة الصيانة
    if [ -d "dist" ]; then
        cp maintenance.html dist/index.html
        print_success "تم تفعيل وضع الصيانة - الموقع الآن يظهر صفحة الصيانة"
    else
        mkdir -p dist
        cp maintenance.html dist/index.html
        print_success "تم تفعيل وضع الصيانة"
    fi

    # إعادة تحميل nginx
    nginx -s reload 2>/dev/null || systemctl reload nginx 2>/dev/null
}

# Main script logic
case "$1" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    logs-api)
        show_api_logs
        ;;
    logs-web)
        show_web_logs
        ;;
    reload)
        reload_services
        ;;
    save)
        save_state
        ;;
    monit)
        monitor_services
        ;;
    cleanup)
        cleanup_processes
        ;;
    stop-site)
        stop_complete_site
        ;;
    start-site)
        start_complete_site
        ;;
    maintenance)
        enable_maintenance_mode
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "أمر غير صحيح: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
