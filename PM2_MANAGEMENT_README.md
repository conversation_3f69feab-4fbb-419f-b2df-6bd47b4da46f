# دليل إدارة PM2 لموقع عجائب الخبراء

## المشكلة التي تم حلها

كانت المشكلة أن هناك عمليات Node.js تعمل خارج PM2، خاصة خادم Vite التطويري الذي كان يعمل على البورت 5173. هذا يسبب تضارب في البورتات ومنع إيقاف الموقع بشكل صحيح.

## الحل المطبق

تم تحسين ملف `pm2-management.sh` ليتعامل مع هذه المشكلة بالطرق التالية:

### 1. تحسين دالة الإيقاف (stop)
- إيقاف عمليات PM2 أولاً
- البحث عن عمليات إضافية على البورتات 3002, 4173, 5173
- إيقاف العمليات المعلقة تلقائياً
- التحقق من نجاح الإيقاف

### 2. إضافة دالة التنظيف (cleanup)
- حذف جميع عمليات PM2
- إيقاف عمليات Vite المعلقة
- إيقاف عمليات server.js المعلقة
- التحقق النهائي من البورتات

## الأوامر المتاحة

```bash
# تشغيل جميع الخدمات
./pm2-management.sh start

# إيقاف جميع الخدمات (محسن)
./pm2-management.sh stop

# إعادة تشغيل جميع الخدمات
./pm2-management.sh restart

# عرض حالة الخدمات
./pm2-management.sh status

# تنظيف العمليات المعلقة (جديد)
./pm2-management.sh cleanup

# عرض السجلات
./pm2-management.sh logs

# عرض سجلات API فقط
./pm2-management.sh logs-api

# عرض سجلات الواجهة فقط
./pm2-management.sh logs-web

# إعادة تحميل الإعدادات
./pm2-management.sh reload

# حفظ الحالة الحالية
./pm2-management.sh save

# مراقبة الأداء
./pm2-management.sh monit

# عرض المساعدة
./pm2-management.sh help
```

## حل المشاكل الشائعة

### المشكلة: الموقع لا يتوقف رغم تشغيل stop
**الحل:**
```bash
./pm2-management.sh cleanup
```

### المشكلة: هناك عمليات متعددة تعمل على نفس البورت
**الحل:**
```bash
./pm2-management.sh cleanup
./pm2-management.sh start
```

### المشكلة: خادم Vite التطويري يعمل في الخلفية
**الحل:**
```bash
./pm2-management.sh stop
# أو
./pm2-management.sh cleanup
```

## التحقق من حالة البورتات

```bash
# التحقق من البورتات المستخدمة
netstat -tulpn | grep -E ":(3002|4173|5173)"

# التحقق من عمليات Node.js
ps aux | grep node
```

## ملاحظات مهمة

1. **البورت 3002**: خادم API
2. **البورت 4173**: الواجهة الأمامية (الإنتاج)
3. **البورت 5173**: خادم Vite التطويري (يجب إيقافه في الإنتاج)

4. استخدم `cleanup` عند مواجهة مشاكل في الإيقاف
5. تأكد من تشغيل السكريبت بصلاحيات مناسبة
6. راقب السجلات للتأكد من عمل الخدمات بشكل صحيح

## الميزات الجديدة

- **إيقاف ذكي**: يبحث عن العمليات المعلقة ويوقفها تلقائياً
- **تنظيف شامل**: أمر cleanup لحل جميع مشاكل العمليات المعلقة
- **تحقق من البورتات**: يتأكد من إيقاف جميع البورتات المطلوبة
- **رسائل واضحة**: تقارير مفصلة عن حالة العمليات
