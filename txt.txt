kitchens:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/why-choose-us:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/cabinets:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/cabinets:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/why-choose-us:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
kitchens:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
kitchens:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
index-BgdDO3bD.js:10 Uncaught Error: Minified React error #300; visit https://react.dev/errors/300 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.
    at Ol (index-BgdDO3bD.js:10:44545)
    at $l (index-BgdDO3bD.js:10:44434)
    at Mi (index-BgdDO3bD.js:10:70918)
    at Ji (index-BgdDO3bD.js:10:81332)
    at Zc (index-BgdDO3bD.js:10:118596)
    at Yc (index-BgdDO3bD.js:10:118524)
    at Xc (index-BgdDO3bD.js:10:118366)
    at Fc (index-BgdDO3bD.js:10:115176)
    at zu (index-BgdDO3bD.js:10:129516)
    at MessagePort.P (index-BgdDO3bD.js:1:2666)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/cabinets:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/footer:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
api/api/hero:1  Failed to load resource: the server responded with a status of 404 (Not Found)
index-BgdDO3bD.js:10  GET https://khobrakitchens.com/api/api/kitchens 404 (Not Found)
U @ index-BgdDO3bD.js:10
W @ index-BgdDO3bD.js:10
