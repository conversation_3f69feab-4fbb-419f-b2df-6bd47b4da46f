import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'terser',
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // تجميع React و React-DOM
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor';
            }
            // تجميع مكتبات UI
            if (id.includes('framer-motion') || id.includes('swiper')) {
              return 'ui';
            }
            // تجميع React Router
            if (id.includes('react-router')) {
              return 'router';
            }
            // تجميع React Helmet
            if (id.includes('react-helmet')) {
              return 'helmet';
            }
            // باقي المكتبات في vendor
            return 'vendor';
          }
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  server: {
    port: 5173,
    host: true
  },
  preview: {
    port: 4173,
    host: true
  }
})
