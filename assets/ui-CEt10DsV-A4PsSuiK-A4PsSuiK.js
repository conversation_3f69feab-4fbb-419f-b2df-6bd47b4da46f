import{r as t}from"./router-D0oP4OMF.js";var e,s,i={exports:{}},n={};var r=(s||(s=1,i.exports=function(){if(e)return n;e=1;var t=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function i(e,s,i){var n=null;if(void 0!==i&&(n=""+i),void 0!==s.key&&(n=""+s.key),"key"in s)for(var r in i={},s)"key"!==r&&(i[r]=s[r]);else i=s;return s=i.ref,{$$typeof:t,type:e,key:n,ref:void 0!==s?s:null,props:i}}return n.Fragment=s,n.jsx=i,n.jsxs=i,n}()),i.exports);function o(t){return null!==t&&"object"==typeof t&&"constructor"in t&&t.constructor===Object}function a(t,e){void 0===t&&(t={}),void 0===e&&(e={});const s=["__proto__","constructor","prototype"];Object.keys(e).filter(t=>s.indexOf(t)<0).forEach(s=>{void 0===t[s]?t[s]=e[s]:o(e[s])&&o(t[s])&&Object.keys(e[s]).length>0&&a(t[s],e[s])})}const l={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function c(){const t="undefined"!=typeof document?document:{};return a(t,l),t}const d={document:l,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:t=>"undefined"==typeof setTimeout?(t(),null):setTimeout(t,0),cancelAnimationFrame(t){"undefined"!=typeof setTimeout&&clearTimeout(t)}};function u(){const t="undefined"!=typeof window?window:{};return a(t,d),t}function h(t,e){return void 0===e&&(e=0),setTimeout(t,e)}function p(){return Date.now()}function m(t,e){void 0===e&&(e="x");const s=u();let i,n,r;const o=function(t){const e=u();let s;return e.getComputedStyle&&(s=e.getComputedStyle(t,null)),!s&&t.currentStyle&&(s=t.currentStyle),s||(s=t.style),s}(t);return s.WebKitCSSMatrix?(n=o.transform||o.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(t=>t.replace(",",".")).join(", ")),r=new s.WebKitCSSMatrix("none"===n?"":n)):(r=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=r.toString().split(",")),"x"===e&&(n=s.WebKitCSSMatrix?r.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===e&&(n=s.WebKitCSSMatrix?r.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),n||0}function f(t){return"object"==typeof t&&null!==t&&t.constructor&&"Object"===Object.prototype.toString.call(t).slice(8,-1)}function g(t){return"undefined"!=typeof window&&void 0!==window.HTMLElement?t instanceof HTMLElement:t&&(1===t.nodeType||11===t.nodeType)}function v(){const t=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){const i=s<0||arguments.length<=s?void 0:arguments[s];if(null!=i&&!g(i)){const s=Object.keys(Object(i)).filter(t=>e.indexOf(t)<0);for(let e=0,n=s.length;e<n;e+=1){const n=s[e],r=Object.getOwnPropertyDescriptor(i,n);void 0!==r&&r.enumerable&&(f(t[n])&&f(i[n])?i[n].__swiper__?t[n]=i[n]:v(t[n],i[n]):!f(t[n])&&f(i[n])?(t[n]={},i[n].__swiper__?t[n]=i[n]:v(t[n],i[n])):t[n]=i[n])}}}return t}function y(t,e,s){t.style.setProperty(e,s)}function x(t){let{swiper:e,targetPosition:s,side:i}=t;const n=u(),r=-e.translate;let o,a=null;const l=e.params.speed;e.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(e.cssModeFrameID);const c=s>r?"next":"prev",d=(t,e)=>"next"===c&&t>=e||"prev"===c&&t<=e,h=()=>{o=(new Date).getTime(),null===a&&(a=o);const t=Math.max(Math.min((o-a)/l,1),0),c=.5-Math.cos(t*Math.PI)/2;let u=r+c*(s-r);if(d(u,s)&&(u=s),e.wrapperEl.scrollTo({[i]:u}),d(u,s))return e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[i]:u})}),void n.cancelAnimationFrame(e.cssModeFrameID);e.cssModeFrameID=n.requestAnimationFrame(h)};h()}function w(t){return t.querySelector(".swiper-slide-transform")||t.shadowRoot&&t.shadowRoot.querySelector(".swiper-slide-transform")||t}function T(t,e){void 0===e&&(e="");const s=u(),i=[...t.children];return s.HTMLSlotElement&&t instanceof HTMLSlotElement&&i.push(...t.assignedElements()),e?i.filter(t=>t.matches(e)):i}function S(t){try{return}catch(e){}}function b(t,e){void 0===e&&(e=[]);const s=document.createElement(t);return s.classList.add(...Array.isArray(e)?e:function(t){return void 0===t&&(t=""),t.trim().split(" ").filter(t=>!!t.trim())}(e)),s}function P(t,e){return u().getComputedStyle(t,null).getPropertyValue(e)}function E(t){let e,s=t;if(s){for(e=0;null!==(s=s.previousSibling);)1===s.nodeType&&(e+=1);return e}}function M(t,e){const s=[];let i=t.parentElement;for(;i;)e?i.matches(e)&&s.push(i):s.push(i),i=i.parentElement;return s}function C(t,e,s){const i=u();return t["width"===e?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(t,null).getPropertyValue("width"===e?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(t,null).getPropertyValue("width"===e?"margin-left":"margin-bottom"))}function A(t){return(Array.isArray(t)?t:[t]).filter(t=>!!t)}function k(t){return e=>Math.abs(e)>0&&t.browser&&t.browser.need3dFix&&Math.abs(e)%90==0?e+.001:e}function V(t,e){void 0===e&&(e=""),"undefined"!=typeof trustedTypes?t.innerHTML=trustedTypes.createPolicy("html",{createHTML:t=>t}).createHTML(e):t.innerHTML=e}let L,D,I;function O(){return L||(L=function(){const t=u(),e=c();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}()),L}function R(t){return void 0===t&&(t={}),D||(D=function(t){let{userAgent:e}=void 0===t?{}:t;const s=O(),i=u(),n=i.navigator.platform,r=e||i.navigator.userAgent,o={ios:!1,android:!1},a=i.screen.width,l=i.screen.height,c=r.match(/(Android);?[\s\/]+([\d.]+)?/);let d=r.match(/(iPad).*OS\s([\d_]+)/);const h=r.match(/(iPod)(.*OS\s([\d_]+))?/),p=!d&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="Win32"===n;let f="MacIntel"===n;return!d&&f&&s.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${a}x${l}`)>=0&&(d=r.match(/(Version)\/([\d.]+)/),d||(d=[0,1,"13_0_0"]),f=!1),c&&!m&&(o.os="android",o.android=!0),(d||p||h)&&(o.os="ios",o.ios=!0),o}(t)),D}function B(){return I||(I=function(){const t=u(),e=R();let s=!1;function i(){const e=t.navigator.userAgent.toLowerCase();return e.indexOf("safari")>=0&&e.indexOf("chrome")<0&&e.indexOf("android")<0}if(i()){const e=String(t.navigator.userAgent);if(e.includes("Version/")){const[t,i]=e.split("Version/")[1].split(" ")[0].split(".").map(t=>Number(t));s=t<16||16===t&&i<2}}const n=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent),r=i();return{isSafari:s||r,needPerspectiveFix:s,need3dFix:r||n&&e.ios,isWebView:n}}()),I}const F=(t,e,s)=>{e&&!t.classList.contains(s)?t.classList.add(s):!e&&t.classList.contains(s)&&t.classList.remove(s)};const j=(t,e,s)=>{e&&!t.classList.contains(s)?t.classList.add(s):!e&&t.classList.contains(s)&&t.classList.remove(s)};const z=(t,e)=>{if(!t||t.destroyed||!t.params)return;const s=e.closest(t.isElement?"swiper-slide":`.${t.params.slideClass}`);if(s){let e=s.querySelector(`.${t.params.lazyPreloaderClass}`);!e&&t.isElement&&(s.shadowRoot?e=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(e=s.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`),e&&e.remove())})),e&&e.remove()}},N=(t,e)=>{if(!t.slides[e])return;const s=t.slides[e].querySelector('[loading="lazy"]');s&&s.removeAttribute("loading")},G=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const s=t.slides.length;if(!s||!e||e<0)return;e=Math.min(e,s);const i="auto"===t.params.slidesPerView?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),n=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const s=n,r=[s-e];return r.push(...Array.from({length:e}).map((t,e)=>s+i+e)),void t.slides.forEach((e,s)=>{r.includes(e.column)&&N(t,s)})}const r=n+i-1;if(t.params.rewind||t.params.loop)for(let o=n-e;o<=r+e;o+=1){const e=(o%s+s)%s;(e<n||e>r)&&N(t,e)}else for(let o=Math.max(n-e,0);o<=Math.min(r+e,s-1);o+=1)o!==n&&(o>r||o<n)&&N(t,o)};function $(t){let{swiper:e,runCallbacks:s,direction:i,step:n}=t;const{activeIndex:r,previousIndex:o}=e;let a=i;a||(a=r>o?"next":r<o?"prev":"reset"),e.emit(`transition${n}`),s&&"reset"===a?e.emit(`slideResetTransition${n}`):s&&r!==o&&(e.emit(`slideChangeTransition${n}`),"next"===a?e.emit(`slideNextTransition${n}`):e.emit(`slidePrevTransition${n}`))}function _(t,e,s){const i=u(),{params:n}=t,r=n.edgeSwipeDetection,o=n.edgeSwipeThreshold;return!r||!(s<=o||s>=i.innerWidth-o)||"prevent"===r&&(e.preventDefault(),!0)}function W(t){const e=this,s=c();let i=t;i.originalEvent&&(i=i.originalEvent);const n=e.touchEventsData;if("pointerdown"===i.type){if(null!==n.pointerId&&n.pointerId!==i.pointerId)return;n.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(n.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type)return void _(e,i,i.targetTouches[0].pageX);const{params:r,touches:o,enabled:a}=e;if(!a)return;if(!r.simulateTouch&&"mouse"===i.pointerType)return;if(e.animating&&r.preventInteractionOnTransition)return;!e.animating&&r.cssMode&&r.loop&&e.loopFix();let l=i.target;if("wrapper"===r.touchEventsTarget&&!function(t,e){const s=u();let i=e.contains(t);!i&&s.HTMLSlotElement&&e instanceof HTMLSlotElement&&(i=[...e.assignedElements()].includes(t),i||(i=function(t,e){const s=[e];for(;s.length>0;){const e=s.shift();if(t===e)return!0;s.push(...e.children,...e.shadowRoot?e.shadowRoot.children:[],...e.assignedElements?e.assignedElements():[])}}(t,e)));return i}(l,e.wrapperEl))return;if("which"in i&&3===i.which)return;if("button"in i&&i.button>0)return;if(n.isTouched&&n.isMoved)return;const d=!!r.noSwipingClass&&""!==r.noSwipingClass,h=i.composedPath?i.composedPath():i.path;d&&i.target&&i.target.shadowRoot&&h&&(l=h[0]);const m=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,f=!(!i.target||!i.target.shadowRoot);if(r.noSwiping&&(f?function(t,e){return void 0===e&&(e=this),function e(s){if(!s||s===c()||s===u())return null;s.assignedSlot&&(s=s.assignedSlot);const i=s.closest(t);return i||s.getRootNode?i||e(s.getRootNode().host):null}(e)}(m,l):l.closest(m)))return void(e.allowClick=!0);if(r.swipeHandler&&!l.closest(r.swipeHandler))return;o.currentX=i.pageX,o.currentY=i.pageY;const g=o.currentX,v=o.currentY;if(!_(e,i,g))return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=g,o.startY=v,n.touchStartTime=p(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);let y=!0;l.matches(n.focusableElements)&&(y=!1,"SELECT"===l.nodeName&&(n.isTouched=!1)),s.activeElement&&s.activeElement.matches(n.focusableElements)&&s.activeElement!==l&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!l.matches(n.focusableElements))&&s.activeElement.blur();const x=y&&e.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!x||l.isContentEditable||i.preventDefault(),r.freeMode&&r.freeMode.enabled&&e.freeMode&&e.animating&&!r.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",i)}function U(t){const e=c(),s=this,i=s.touchEventsData,{params:n,touches:r,rtlTranslate:o,enabled:a}=s;if(!a)return;if(!n.simulateTouch&&"mouse"===t.pointerType)return;let l,d=t;if(d.originalEvent&&(d=d.originalEvent),"pointermove"===d.type){if(null!==i.touchId)return;if(d.pointerId!==i.pointerId)return}if("touchmove"===d.type){if(l=[...d.changedTouches].find(t=>t.identifier===i.touchId),!l||l.identifier!==i.touchId)return}else l=d;if(!i.isTouched)return void(i.startMoving&&i.isScrolling&&s.emit("touchMoveOpposite",d));const u=l.pageX,h=l.pageY;if(d.preventedByNestedSwiper)return r.startX=u,void(r.startY=h);if(!s.allowTouchMove)return d.target.matches(i.focusableElements)||(s.allowClick=!1),void(i.isTouched&&(Object.assign(r,{startX:u,startY:h,currentX:u,currentY:h}),i.touchStartTime=p()));if(n.touchReleaseOnEdges&&!n.loop)if(s.isVertical()){if(h<r.startY&&s.translate<=s.maxTranslate()||h>r.startY&&s.translate>=s.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else{if(o&&(u>r.startX&&-s.translate<=s.maxTranslate()||u<r.startX&&-s.translate>=s.minTranslate()))return;if(!o&&(u<r.startX&&s.translate<=s.maxTranslate()||u>r.startX&&s.translate>=s.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(i.focusableElements)&&e.activeElement!==d.target&&"mouse"!==d.pointerType&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(i.focusableElements))return i.isMoved=!0,void(s.allowClick=!1);i.allowTouchCallbacks&&s.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=u,r.currentY=h;const m=r.currentX-r.startX,f=r.currentY-r.startY;if(s.params.threshold&&Math.sqrt(m**2+f**2)<s.params.threshold)return;if(void 0===i.isScrolling){let t;s.isHorizontal()&&r.currentY===r.startY||s.isVertical()&&r.currentX===r.startX?i.isScrolling=!1:m*m+f*f>=25&&(t=180*Math.atan2(Math.abs(f),Math.abs(m))/Math.PI,i.isScrolling=s.isHorizontal()?t>n.touchAngle:90-t>n.touchAngle)}if(i.isScrolling&&s.emit("touchMoveOpposite",d),void 0===i.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(i.startMoving=!0)),i.isScrolling||"touchmove"===d.type&&i.preventTouchMoveFromPointerMove)return void(i.isTouched=!1);if(!i.startMoving)return;s.allowClick=!1,!n.cssMode&&d.cancelable&&d.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&d.stopPropagation();let g=s.isHorizontal()?m:f,v=s.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(g=Math.abs(g)*(o?1:-1),v=Math.abs(v)*(o?1:-1)),r.diff=g,g*=n.touchRatio,o&&(g=-g,v=-v);const y=s.touchesDirection;s.swipeDirection=g>0?"prev":"next",s.touchesDirection=v>0?"prev":"next";const x=s.params.loop&&!n.cssMode,w="next"===s.touchesDirection&&s.allowSlideNext||"prev"===s.touchesDirection&&s.allowSlidePrev;if(!i.isMoved){if(x&&w&&s.loopFix({direction:s.swipeDirection}),i.startTranslate=s.getTranslate(),s.setTransition(0),s.animating){const t=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});s.wrapperEl.dispatchEvent(t)}i.allowMomentumBounce=!1,!n.grabCursor||!0!==s.allowSlideNext&&!0!==s.allowSlidePrev||s.setGrabCursor(!0),s.emit("sliderFirstMove",d)}if((new Date).getTime(),!1!==n._loopSwapReset&&i.isMoved&&i.allowThresholdMove&&y!==s.touchesDirection&&x&&w&&Math.abs(g)>=1)return Object.assign(r,{startX:u,startY:h,currentX:u,currentY:h,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,void(i.startTranslate=i.currentTranslate);s.emit("sliderMove",d),i.isMoved=!0,i.currentTranslate=g+i.startTranslate;let T=!0,S=n.resistanceRatio;if(n.touchReleaseOnEdges&&(S=0),g>0?(x&&w&&i.allowThresholdMove&&i.currentTranslate>(n.centeredSlides?s.minTranslate()-s.slidesSizesGrid[s.activeIndex+1]-("auto"!==n.slidesPerView&&s.slides.length-n.slidesPerView>=2?s.slidesSizesGrid[s.activeIndex+1]+s.params.spaceBetween:0)-s.params.spaceBetween:s.minTranslate())&&s.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>s.minTranslate()&&(T=!1,n.resistance&&(i.currentTranslate=s.minTranslate()-1+(-s.minTranslate()+i.startTranslate+g)**S))):g<0&&(x&&w&&i.allowThresholdMove&&i.currentTranslate<(n.centeredSlides?s.maxTranslate()+s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween+("auto"!==n.slidesPerView&&s.slides.length-n.slidesPerView>=2?s.slidesSizesGrid[s.slidesSizesGrid.length-1]+s.params.spaceBetween:0):s.maxTranslate())&&s.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:s.slides.length-("auto"===n.slidesPerView?s.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),i.currentTranslate<s.maxTranslate()&&(T=!1,n.resistance&&(i.currentTranslate=s.maxTranslate()+1-(s.maxTranslate()-i.startTranslate-g)**S))),T&&(d.preventedByNestedSwiper=!0),!s.allowSlideNext&&"next"===s.swipeDirection&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!s.allowSlidePrev&&"prev"===s.swipeDirection&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),s.allowSlidePrev||s.allowSlideNext||(i.currentTranslate=i.startTranslate),n.threshold>0){if(!(Math.abs(g)>n.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,i.currentTranslate=i.startTranslate,void(r.diff=s.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&s.freeMode||n.watchSlidesProgress)&&(s.updateActiveIndex(),s.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&s.freeMode&&s.freeMode.onTouchMove(),s.updateProgress(i.currentTranslate),s.setTranslate(i.currentTranslate))}function H(t){const e=this,s=e.touchEventsData;let i,n=t;n.originalEvent&&(n=n.originalEvent);if("touchend"===n.type||"touchcancel"===n.type){if(i=[...n.changedTouches].find(t=>t.identifier===s.touchId),!i||i.identifier!==s.touchId)return}else{if(null!==s.touchId)return;if(n.pointerId!==s.pointerId)return;i=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)){if(!(["pointercancel","contextmenu"].includes(n.type)&&(e.browser.isSafari||e.browser.isWebView)))return}s.pointerId=null,s.touchId=null;const{params:r,touches:o,rtlTranslate:a,slidesGrid:l,enabled:c}=e;if(!c)return;if(!r.simulateTouch&&"mouse"===n.pointerType)return;if(s.allowTouchCallbacks&&e.emit("touchEnd",n),s.allowTouchCallbacks=!1,!s.isTouched)return s.isMoved&&r.grabCursor&&e.setGrabCursor(!1),s.isMoved=!1,void(s.startMoving=!1);r.grabCursor&&s.isMoved&&s.isTouched&&(!0===e.allowSlideNext||!0===e.allowSlidePrev)&&e.setGrabCursor(!1);const d=p(),u=d-s.touchStartTime;if(e.allowClick){const t=n.path||n.composedPath&&n.composedPath();e.updateClickedSlide(t&&t[0]||n.target,t),e.emit("tap click",n),u<300&&d-s.lastClickTime<300&&e.emit("doubleTap doubleClick",n)}if(s.lastClickTime=p(),h(()=>{e.destroyed||(e.allowClick=!0)}),!s.isTouched||!s.isMoved||!e.swipeDirection||0===o.diff&&!s.loopSwapReset||s.currentTranslate===s.startTranslate&&!s.loopSwapReset)return s.isTouched=!1,s.isMoved=!1,void(s.startMoving=!1);let m;if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,m=r.followFinger?a?e.translate:-e.translate:-s.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled)return void e.freeMode.onTouchEnd({currentPos:m});const f=m>=-e.maxTranslate()&&!e.params.loop;let g=0,v=e.slidesSizesGrid[0];for(let h=0;h<l.length;h+=h<r.slidesPerGroupSkip?1:r.slidesPerGroup){const t=h<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==l[h+t]?(f||m>=l[h]&&m<l[h+t])&&(g=h,v=l[h+t]-l[h]):(f||m>=l[h])&&(g=h,v=l[l.length-1]-l[l.length-2])}let y=null,x=null;r.rewind&&(e.isBeginning?x=r.virtual&&r.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(y=0));const w=(m-l[g])/v,T=g<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(u>r.longSwipesMs){if(!r.longSwipes)return void e.slideTo(e.activeIndex);"next"===e.swipeDirection&&(w>=r.longSwipesRatio?e.slideTo(r.rewind&&e.isEnd?y:g+T):e.slideTo(g)),"prev"===e.swipeDirection&&(w>1-r.longSwipesRatio?e.slideTo(g+T):null!==x&&w<0&&Math.abs(w)>r.longSwipesRatio?e.slideTo(x):e.slideTo(g))}else{if(!r.shortSwipes)return void e.slideTo(e.activeIndex);e.navigation&&(n.target===e.navigation.nextEl||n.target===e.navigation.prevEl)?n.target===e.navigation.nextEl?e.slideTo(g+T):e.slideTo(g):("next"===e.swipeDirection&&e.slideTo(null!==y?y:g+T),"prev"===e.swipeDirection&&e.slideTo(null!==x?x:g))}}function X(){const t=this,{params:e,el:s}=t;if(s&&0===s.offsetWidth)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:n,snapGrid:r}=t,o=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const a=o&&e.loop;!("auto"===e.slidesPerView||e.slidesPerView>1)||!t.isEnd||t.isBeginning||t.params.centeredSlides||a?t.params.loop&&!o?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0):t.slideTo(t.slides.length-1,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout(()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()},500)),t.allowSlidePrev=n,t.allowSlideNext=i,t.params.watchOverflow&&r!==t.snapGrid&&t.checkOverflow()}function Y(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function q(){const t=this,{wrapperEl:e,rtlTranslate:s,enabled:i}=t;if(!i)return;let n;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,0===t.translate&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();const r=t.maxTranslate()-t.minTranslate();n=0===r?0:(t.translate-t.minTranslate())/r,n!==t.progress&&t.updateProgress(s?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function K(t){const e=this;z(e,t.target),e.params.cssMode||"auto"!==e.params.slidesPerView&&!e.params.autoHeight||e.update()}function Z(){const t=this;t.documentTouchHandlerProceeded||(t.documentTouchHandlerProceeded=!0,t.params.touchReleaseOnEdges&&(t.el.style.touchAction="auto"))}const J=(t,e)=>{const s=c(),{params:i,el:n,wrapperEl:r,device:o}=t,a=!!i.nested,l="on"===e?"addEventListener":"removeEventListener",d=e;n&&"string"!=typeof n&&(s[l]("touchstart",t.onDocumentTouchStart,{passive:!1,capture:a}),n[l]("touchstart",t.onTouchStart,{passive:!1}),n[l]("pointerdown",t.onTouchStart,{passive:!1}),s[l]("touchmove",t.onTouchMove,{passive:!1,capture:a}),s[l]("pointermove",t.onTouchMove,{passive:!1,capture:a}),s[l]("touchend",t.onTouchEnd,{passive:!0}),s[l]("pointerup",t.onTouchEnd,{passive:!0}),s[l]("pointercancel",t.onTouchEnd,{passive:!0}),s[l]("touchcancel",t.onTouchEnd,{passive:!0}),s[l]("pointerout",t.onTouchEnd,{passive:!0}),s[l]("pointerleave",t.onTouchEnd,{passive:!0}),s[l]("contextmenu",t.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&n[l]("click",t.onClick,!0),i.cssMode&&r[l]("scroll",t.onScroll),i.updateOnWindowResize?t[d](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",X,!0):t[d]("observerUpdate",X,!0),n[l]("load",t.onLoad,{capture:!0}))};const Q=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;var tt={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function et(t,e){return function(s){void 0===s&&(s={});const i=Object.keys(s)[0],n=s[i];"object"==typeof n&&null!==n?(!0===t[i]&&(t[i]={enabled:!0}),"navigation"===i&&t[i]&&t[i].enabled&&!t[i].prevEl&&!t[i].nextEl&&(t[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&t[i]&&t[i].enabled&&!t[i].el&&(t[i].auto=!0),i in t&&"enabled"in n?("object"!=typeof t[i]||"enabled"in t[i]||(t[i].enabled=!0),t[i]||(t[i]={enabled:!1}),v(e,s)):v(e,s)):v(e,s)}}const st={eventsEmitter:{on(t,e,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof e)return i;const n=s?"unshift":"push";return t.split(" ").forEach(t=>{i.eventsListeners[t]||(i.eventsListeners[t]=[]),i.eventsListeners[t][n](e)}),i},once(t,e,s){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof e)return i;function n(){i.off(t,n),n.__emitterProxy&&delete n.__emitterProxy;for(var s=arguments.length,r=new Array(s),o=0;o<s;o++)r[o]=arguments[o];e.apply(i,r)}return n.__emitterProxy=e,i.on(t,n,s)},onAny(t,e){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof t)return s;const i=e?"unshift":"push";return s.eventsAnyListeners.indexOf(t)<0&&s.eventsAnyListeners[i](t),s},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsAnyListeners)return e;const s=e.eventsAnyListeners.indexOf(t);return s>=0&&e.eventsAnyListeners.splice(s,1),e},off(t,e){const s=this;return!s.eventsListeners||s.destroyed?s:s.eventsListeners?(t.split(" ").forEach(t=>{void 0===e?s.eventsListeners[t]=[]:s.eventsListeners[t]&&s.eventsListeners[t].forEach((i,n)=>{(i===e||i.__emitterProxy&&i.__emitterProxy===e)&&s.eventsListeners[t].splice(n,1)})}),s):s},emit(){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsListeners)return t;let e,s,i;for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];"string"==typeof r[0]||Array.isArray(r[0])?(e=r[0],s=r.slice(1,r.length),i=t):(e=r[0].events,s=r[0].data,i=r[0].context||t),s.unshift(i);return(Array.isArray(e)?e:e.split(" ")).forEach(e=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach(t=>{t.apply(i,[e,...s])}),t.eventsListeners&&t.eventsListeners[e]&&t.eventsListeners[e].forEach(t=>{t.apply(i,s)})}),t}},update:{updateSize:function(){const t=this;let e,s;const i=t.el;e=void 0!==t.params.width&&null!==t.params.width?t.params.width:i.clientWidth,s=void 0!==t.params.height&&null!==t.params.height?t.params.height:i.clientHeight,0===e&&t.isHorizontal()||0===s&&t.isVertical()||(e=e-parseInt(P(i,"padding-left")||0,10)-parseInt(P(i,"padding-right")||0,10),s=s-parseInt(P(i,"padding-top")||0,10)-parseInt(P(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(s)&&(s=0),Object.assign(t,{width:e,height:s,size:t.isHorizontal()?e:s}))},updateSlides:function(){const t=this;function e(e,s){return parseFloat(e.getPropertyValue(t.getDirectionLabel(s))||0)}const s=t.params,{wrapperEl:i,slidesEl:n,size:r,rtlTranslate:o,wrongRTL:a}=t,l=t.virtual&&s.virtual.enabled,c=l?t.virtual.slides.length:t.slides.length,d=T(n,`.${t.params.slideClass}, swiper-slide`),u=l?t.virtual.slides.length:d.length;let h=[];const p=[],m=[];let f=s.slidesOffsetBefore;"function"==typeof f&&(f=s.slidesOffsetBefore.call(t));let g=s.slidesOffsetAfter;"function"==typeof g&&(g=s.slidesOffsetAfter.call(t));const v=t.snapGrid.length,x=t.slidesGrid.length;let w=s.spaceBetween,S=-f,b=0,E=0;if(void 0===r)return;"string"==typeof w&&w.indexOf("%")>=0?w=parseFloat(w.replace("%",""))/100*r:"string"==typeof w&&(w=parseFloat(w)),t.virtualSize=-w,d.forEach(t=>{o?t.style.marginLeft="":t.style.marginRight="",t.style.marginBottom="",t.style.marginTop=""}),s.centeredSlides&&s.cssMode&&(y(i,"--swiper-centered-offset-before",""),y(i,"--swiper-centered-offset-after",""));const M=s.grid&&s.grid.rows>1&&t.grid;let A;M?t.grid.initSlides(d):t.grid&&t.grid.unsetSlides();const k="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter(t=>void 0!==s.breakpoints[t].slidesPerView).length>0;for(let y=0;y<u;y+=1){let i;if(A=0,d[y]&&(i=d[y]),M&&t.grid.updateSlide(y,i,d),!d[y]||"none"!==P(i,"display")){if("auto"===s.slidesPerView){k&&(d[y].style[t.getDirectionLabel("width")]="");const n=getComputedStyle(i),r=i.style.transform,o=i.style.webkitTransform;if(r&&(i.style.transform="none"),o&&(i.style.webkitTransform="none"),s.roundLengths)A=t.isHorizontal()?C(i,"width"):C(i,"height");else{const t=e(n,"width"),s=e(n,"padding-left"),r=e(n,"padding-right"),o=e(n,"margin-left"),a=e(n,"margin-right"),l=n.getPropertyValue("box-sizing");if(l&&"border-box"===l)A=t+o+a;else{const{clientWidth:e,offsetWidth:n}=i;A=t+s+r+o+a+(n-e)}}r&&(i.style.transform=r),o&&(i.style.webkitTransform=o),s.roundLengths&&(A=Math.floor(A))}else A=(r-(s.slidesPerView-1)*w)/s.slidesPerView,s.roundLengths&&(A=Math.floor(A)),d[y]&&(d[y].style[t.getDirectionLabel("width")]=`${A}px`);d[y]&&(d[y].swiperSlideSize=A),m.push(A),s.centeredSlides?(S=S+A/2+b/2+w,0===b&&0!==y&&(S=S-r/2-w),0===y&&(S=S-r/2-w),Math.abs(S)<.001&&(S=0),s.roundLengths&&(S=Math.floor(S)),E%s.slidesPerGroup===0&&h.push(S),p.push(S)):(s.roundLengths&&(S=Math.floor(S)),(E-Math.min(t.params.slidesPerGroupSkip,E))%t.params.slidesPerGroup===0&&h.push(S),p.push(S),S=S+A+w),t.virtualSize+=A+w,b=A,E+=1}}if(t.virtualSize=Math.max(t.virtualSize,r)+g,o&&a&&("slide"===s.effect||"coverflow"===s.effect)&&(i.style.width=`${t.virtualSize+w}px`),s.setWrapperSize&&(i.style[t.getDirectionLabel("width")]=`${t.virtualSize+w}px`),M&&t.grid.updateWrapperSize(A,h),!s.centeredSlides){const e=[];for(let i=0;i<h.length;i+=1){let n=h[i];s.roundLengths&&(n=Math.floor(n)),h[i]<=t.virtualSize-r&&e.push(n)}h=e,Math.floor(t.virtualSize-r)-Math.floor(h[h.length-1])>1&&h.push(t.virtualSize-r)}if(l&&s.loop){const e=m[0]+w;if(s.slidesPerGroup>1){const i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),n=e*s.slidesPerGroup;for(let t=0;t<i;t+=1)h.push(h[h.length-1]+n)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&h.push(h[h.length-1]+e),p.push(p[p.length-1]+e),t.virtualSize+=e}if(0===h.length&&(h=[0]),0!==w){const e=t.isHorizontal()&&o?"marginLeft":t.getDirectionLabel("marginRight");d.filter((t,e)=>!(s.cssMode&&!s.loop)||e!==d.length-1).forEach(t=>{t.style[e]=`${w}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let t=0;m.forEach(e=>{t+=e+(w||0)}),t-=w;const e=t>r?t-r:0;h=h.map(t=>t<=0?-f:t>e?e+g:t)}if(s.centerInsufficientSlides){let t=0;m.forEach(e=>{t+=e+(w||0)}),t-=w;const e=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(t+e<r){const s=(r-t-e)/2;h.forEach((t,e)=>{h[e]=t-s}),p.forEach((t,e)=>{p[e]=t+s})}}if(Object.assign(t,{slides:d,snapGrid:h,slidesGrid:p,slidesSizesGrid:m}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){y(i,"--swiper-centered-offset-before",-h[0]+"px"),y(i,"--swiper-centered-offset-after",t.size/2-m[m.length-1]/2+"px");const e=-t.snapGrid[0],s=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(t=>t+s)}if(u!==c&&t.emit("slidesLengthChange"),h.length!==v&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),p.length!==x&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!(l||s.cssMode||"slide"!==s.effect&&"fade"!==s.effect)){const e=`${s.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);u<=s.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(t){const e=this,s=[],i=e.virtual&&e.params.virtual.enabled;let n,r=0;"number"==typeof t?e.setTransition(t):!0===t&&e.setTransition(e.params.speed);const o=t=>i?e.slides[e.getSlideIndexByData(t)]:e.slides[t];if("auto"!==e.params.slidesPerView&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(t=>{s.push(t)});else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){const t=e.activeIndex+n;if(t>e.slides.length&&!i)break;s.push(o(t))}else s.push(o(e.activeIndex));for(n=0;n<s.length;n+=1)if(void 0!==s[n]){const t=s[n].offsetHeight;r=t>r?t:r}(r||0===r)&&(e.wrapperEl.style.height=`${r}px`)},updateSlidesOffset:function(){const t=this,e=t.slides,s=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(t.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-s-t.cssOverflowAdjustment()},updateSlidesProgress:function(t){void 0===t&&(t=this&&this.translate||0);const e=this,s=e.params,{slides:i,rtlTranslate:n,snapGrid:r}=e;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&e.updateSlidesOffset();let o=-t;n&&(o=t),e.visibleSlidesIndexes=[],e.visibleSlides=[];let a=s.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*e.size:"string"==typeof a&&(a=parseFloat(a));for(let l=0;l<i.length;l+=1){const t=i[l];let c=t.swiperSlideOffset;s.cssMode&&s.centeredSlides&&(c-=i[0].swiperSlideOffset);const d=(o+(s.centeredSlides?e.minTranslate():0)-c)/(t.swiperSlideSize+a),u=(o-r[0]+(s.centeredSlides?e.minTranslate():0)-c)/(t.swiperSlideSize+a),h=-(o-c),p=h+e.slidesSizesGrid[l],m=h>=0&&h<=e.size-e.slidesSizesGrid[l],f=h>=0&&h<e.size-1||p>1&&p<=e.size||h<=0&&p>=e.size;f&&(e.visibleSlides.push(t),e.visibleSlidesIndexes.push(l)),F(t,f,s.slideVisibleClass),F(t,m,s.slideFullyVisibleClass),t.progress=n?-d:d,t.originalProgress=n?-u:u}},updateProgress:function(t){const e=this;if(void 0===t){const s=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*s||0}const s=e.params,i=e.maxTranslate()-e.minTranslate();let{progress:n,isBeginning:r,isEnd:o,progressLoop:a}=e;const l=r,c=o;if(0===i)n=0,r=!0,o=!0;else{n=(t-e.minTranslate())/i;const s=Math.abs(t-e.minTranslate())<1,a=Math.abs(t-e.maxTranslate())<1;r=s||n<=0,o=a||n>=1,s&&(n=0),a&&(n=1)}if(s.loop){const s=e.getSlideIndexByData(0),i=e.getSlideIndexByData(e.slides.length-1),n=e.slidesGrid[s],r=e.slidesGrid[i],o=e.slidesGrid[e.slidesGrid.length-1],l=Math.abs(t);a=l>=n?(l-n)/o:(l+o-r)/o,a>1&&(a-=1)}Object.assign(e,{progress:n,progressLoop:a,isBeginning:r,isEnd:o}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&e.updateSlidesProgress(t),r&&!l&&e.emit("reachBeginning toEdge"),o&&!c&&e.emit("reachEnd toEdge"),(l&&!r||c&&!o)&&e.emit("fromEdge"),e.emit("progress",n)},updateSlidesClasses:function(){const t=this,{slides:e,params:s,slidesEl:i,activeIndex:n}=t,r=t.virtual&&s.virtual.enabled,o=t.grid&&s.grid&&s.grid.rows>1,a=t=>T(i,`.${s.slideClass}${t}, swiper-slide${t}`)[0];let l,c,d;if(r)if(s.loop){let e=n-t.virtual.slidesBefore;e<0&&(e=t.virtual.slides.length+e),e>=t.virtual.slides.length&&(e-=t.virtual.slides.length),l=a(`[data-swiper-slide-index="${e}"]`)}else l=a(`[data-swiper-slide-index="${n}"]`);else o?(l=e.find(t=>t.column===n),d=e.find(t=>t.column===n+1),c=e.find(t=>t.column===n-1)):l=e[n];l&&(o||(d=function(t,e){const s=[];for(;t.nextElementSibling;){const i=t.nextElementSibling;e?i.matches(e)&&s.push(i):s.push(i),t=i}return s}(l,`.${s.slideClass}, swiper-slide`)[0],s.loop&&!d&&(d=e[0]),c=function(t,e){const s=[];for(;t.previousElementSibling;){const i=t.previousElementSibling;e?i.matches(e)&&s.push(i):s.push(i),t=i}return s}(l,`.${s.slideClass}, swiper-slide`)[0],s.loop&&0===!c&&(c=e[e.length-1]))),e.forEach(t=>{j(t,t===l,s.slideActiveClass),j(t,t===d,s.slideNextClass),j(t,t===c,s.slidePrevClass)}),t.emitSlidesClasses()},updateActiveIndex:function(t){const e=this,s=e.rtlTranslate?e.translate:-e.translate,{snapGrid:i,params:n,activeIndex:r,realIndex:o,snapIndex:a}=e;let l,c=t;const d=t=>{let s=t-e.virtual.slidesBefore;return s<0&&(s=e.virtual.slides.length+s),s>=e.virtual.slides.length&&(s-=e.virtual.slides.length),s};if(void 0===c&&(c=function(t){const{slidesGrid:e,params:s}=t,i=t.rtlTranslate?t.translate:-t.translate;let n;for(let r=0;r<e.length;r+=1)void 0!==e[r+1]?i>=e[r]&&i<e[r+1]-(e[r+1]-e[r])/2?n=r:i>=e[r]&&i<e[r+1]&&(n=r+1):i>=e[r]&&(n=r);return s.normalizeSlideIndex&&(n<0||void 0===n)&&(n=0),n}(e)),i.indexOf(s)>=0)l=i.indexOf(s);else{const t=Math.min(n.slidesPerGroupSkip,c);l=t+Math.floor((c-t)/n.slidesPerGroup)}if(l>=i.length&&(l=i.length-1),c===r&&!e.params.loop)return void(l!==a&&(e.snapIndex=l,e.emit("snapIndexChange")));if(c===r&&e.params.loop&&e.virtual&&e.params.virtual.enabled)return void(e.realIndex=d(c));const u=e.grid&&n.grid&&n.grid.rows>1;let h;if(e.virtual&&n.virtual.enabled&&n.loop)h=d(c);else if(u){const t=e.slides.find(t=>t.column===c);let s=parseInt(t.getAttribute("data-swiper-slide-index"),10);Number.isNaN(s)&&(s=Math.max(e.slides.indexOf(t),0)),h=Math.floor(s/n.grid.rows)}else if(e.slides[c]){const t=e.slides[c].getAttribute("data-swiper-slide-index");h=t?parseInt(t,10):c}else h=c;Object.assign(e,{previousSnapIndex:a,snapIndex:l,previousRealIndex:o,realIndex:h,previousIndex:r,activeIndex:c}),e.initialized&&G(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(o!==h&&e.emit("realIndexChange"),e.emit("slideChange"))},updateClickedSlide:function(t,e){const s=this,i=s.params;let n=t.closest(`.${i.slideClass}, swiper-slide`);!n&&s.isElement&&e&&e.length>1&&e.includes(t)&&[...e.slice(e.indexOf(t)+1,e.length)].forEach(t=>{!n&&t.matches&&t.matches(`.${i.slideClass}, swiper-slide`)&&(n=t)});let r,o=!1;if(n)for(let a=0;a<s.slides.length;a+=1)if(s.slides[a]===n){o=!0,r=a;break}if(!n||!o)return s.clickedSlide=void 0,void(s.clickedIndex=void 0);s.clickedSlide=n,s.virtual&&s.params.virtual.enabled?s.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):s.clickedIndex=r,i.slideToClickedSlide&&void 0!==s.clickedIndex&&s.clickedIndex!==s.activeIndex&&s.slideToClickedSlide()}},translate:{getTranslate:function(t){void 0===t&&(t=this.isHorizontal()?"x":"y");const{params:e,rtlTranslate:s,translate:i,wrapperEl:n}=this;if(e.virtualTranslate)return s?-i:i;if(e.cssMode)return i;let r=m(n,t);return r+=this.cssOverflowAdjustment(),s&&(r=-r),r||0},setTranslate:function(t,e){const s=this,{rtlTranslate:i,params:n,wrapperEl:r,progress:o}=s;let a,l=0,c=0;s.isHorizontal()?l=i?-t:t:c=t,n.roundLengths&&(l=Math.floor(l),c=Math.floor(c)),s.previousTranslate=s.translate,s.translate=s.isHorizontal()?l:c,n.cssMode?r[s.isHorizontal()?"scrollLeft":"scrollTop"]=s.isHorizontal()?-l:-c:n.virtualTranslate||(s.isHorizontal()?l-=s.cssOverflowAdjustment():c-=s.cssOverflowAdjustment(),r.style.transform=`translate3d(${l}px, ${c}px, 0px)`);const d=s.maxTranslate()-s.minTranslate();a=0===d?0:(t-s.minTranslate())/d,a!==o&&s.updateProgress(t),s.emit("setTranslate",s.translate,e)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(t,e,s,i,n){void 0===t&&(t=0),void 0===e&&(e=this.params.speed),void 0===s&&(s=!0),void 0===i&&(i=!0);const r=this,{params:o,wrapperEl:a}=r;if(r.animating&&o.preventInteractionOnTransition)return!1;const l=r.minTranslate(),c=r.maxTranslate();let d;if(d=i&&t>l?l:i&&t<c?c:t,r.updateProgress(d),o.cssMode){const t=r.isHorizontal();if(0===e)a[t?"scrollLeft":"scrollTop"]=-d;else{if(!r.support.smoothScroll)return x({swiper:r,targetPosition:-d,side:t?"left":"top"}),!0;a.scrollTo({[t?"left":"top"]:-d,behavior:"smooth"})}return!0}return 0===e?(r.setTransition(0),r.setTranslate(d),s&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionEnd"))):(r.setTransition(e),r.setTranslate(d),s&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(t){r&&!r.destroyed&&t.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,s&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(t,e){const s=this;s.params.cssMode||(s.wrapperEl.style.transitionDuration=`${t}ms`,s.wrapperEl.style.transitionDelay=0===t?"0ms":""),s.emit("setTransition",t,e)},transitionStart:function(t,e){void 0===t&&(t=!0);const s=this,{params:i}=s;i.cssMode||(i.autoHeight&&s.updateAutoHeight(),$({swiper:s,runCallbacks:t,direction:e,step:"Start"}))},transitionEnd:function(t,e){void 0===t&&(t=!0);const s=this,{params:i}=s;s.animating=!1,i.cssMode||(s.setTransition(0),$({swiper:s,runCallbacks:t,direction:e,step:"End"}))}},slide:{slideTo:function(t,e,s,i,n){void 0===t&&(t=0),void 0===s&&(s=!0),"string"==typeof t&&(t=parseInt(t,10));const r=this;let o=t;o<0&&(o=0);const{params:a,snapGrid:l,slidesGrid:c,previousIndex:d,activeIndex:u,rtlTranslate:h,wrapperEl:p,enabled:m}=r;if(!m&&!i&&!n||r.destroyed||r.animating&&a.preventInteractionOnTransition)return!1;void 0===e&&(e=r.params.speed);const f=Math.min(r.params.slidesPerGroupSkip,o);let g=f+Math.floor((o-f)/r.params.slidesPerGroup);g>=l.length&&(g=l.length-1);const v=-l[g];if(a.normalizeSlideIndex)for(let x=0;x<c.length;x+=1){const t=-Math.floor(100*v),e=Math.floor(100*c[x]),s=Math.floor(100*c[x+1]);void 0!==c[x+1]?t>=e&&t<s-(s-e)/2?o=x:t>=e&&t<s&&(o=x+1):t>=e&&(o=x)}if(r.initialized&&o!==u){if(!r.allowSlideNext&&(h?v>r.translate&&v>r.minTranslate():v<r.translate&&v<r.minTranslate()))return!1;if(!r.allowSlidePrev&&v>r.translate&&v>r.maxTranslate()&&(u||0)!==o)return!1}let y;o!==(d||0)&&s&&r.emit("beforeSlideChangeStart"),r.updateProgress(v),y=o>u?"next":o<u?"prev":"reset";const w=r.virtual&&r.params.virtual.enabled;if(!(w&&n)&&(h&&-v===r.translate||!h&&v===r.translate))return r.updateActiveIndex(o),a.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==a.effect&&r.setTranslate(v),"reset"!==y&&(r.transitionStart(s,y),r.transitionEnd(s,y)),!1;if(a.cssMode){const t=r.isHorizontal(),s=h?v:-v;if(0===e)w&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),w&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{p[t?"scrollLeft":"scrollTop"]=s})):p[t?"scrollLeft":"scrollTop"]=s,w&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return x({swiper:r,targetPosition:s,side:t?"left":"top"}),!0;p.scrollTo({[t?"left":"top"]:s,behavior:"smooth"})}return!0}const T=B().isSafari;return w&&!n&&T&&r.isElement&&r.virtual.update(!1,!1,o),r.setTransition(e),r.setTranslate(v),r.updateActiveIndex(o),r.updateSlidesClasses(),r.emit("beforeTransitionStart",e,i),r.transitionStart(s,y),0===e?r.transitionEnd(s,y):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(t){r&&!r.destroyed&&t.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(s,y))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(t,e,s,i){if(void 0===t&&(t=0),void 0===s&&(s=!0),"string"==typeof t){t=parseInt(t,10)}const n=this;if(n.destroyed)return;void 0===e&&(e=n.params.speed);const r=n.grid&&n.params.grid&&n.params.grid.rows>1;let o=t;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)o+=n.virtual.slidesBefore;else{let t;if(r){const e=o*n.params.grid.rows;t=n.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e).column}else t=n.getSlideIndexByData(o);const e=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:s}=n.params;let a=n.params.slidesPerView;"auto"===a?a=n.slidesPerViewDynamic():(a=Math.ceil(parseFloat(n.params.slidesPerView,10)),s&&a%2==0&&(a+=1));let l=e-t<a;if(s&&(l=l||t<Math.ceil(a/2)),i&&s&&"auto"!==n.params.slidesPerView&&!r&&(l=!1),l){const i=s?t<n.activeIndex?"prev":"next":t-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:i,slideTo:!0,activeSlideIndex:"next"===i?t+1:t-e+1,slideRealIndex:"next"===i?n.realIndex:void 0})}if(r){const t=o*n.params.grid.rows;o=n.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t).column}else o=n.getSlideIndexByData(o)}return requestAnimationFrame(()=>{n.slideTo(o,e,s,i)}),n},slideNext:function(t,e,s){void 0===e&&(e=!0);const i=this,{enabled:n,params:r,animating:o}=i;if(!n||i.destroyed)return i;void 0===t&&(t=i.params.speed);let a=r.slidesPerGroup;"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(a=Math.max(i.slidesPerViewDynamic("current",!0),1));const l=i.activeIndex<r.slidesPerGroupSkip?1:a,c=i.virtual&&r.virtual.enabled;if(r.loop){if(o&&!c&&r.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+l,t,e,s)}),!0}return r.rewind&&i.isEnd?i.slideTo(0,t,e,s):i.slideTo(i.activeIndex+l,t,e,s)},slidePrev:function(t,e,s){void 0===e&&(e=!0);const i=this,{params:n,snapGrid:r,slidesGrid:o,rtlTranslate:a,enabled:l,animating:c}=i;if(!l||i.destroyed)return i;void 0===t&&(t=i.params.speed);const d=i.virtual&&n.virtual.enabled;if(n.loop){if(c&&!d&&n.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}function u(t){return t<0?-Math.floor(Math.abs(t)):Math.floor(t)}const h=u(a?i.translate:-i.translate),p=r.map(t=>u(t)),m=n.freeMode&&n.freeMode.enabled;let f=r[p.indexOf(h)-1];if(void 0===f&&(n.cssMode||m)){let t;r.forEach((e,s)=>{h>=e&&(t=s)}),void 0!==t&&(f=m?r[t]:r[t>0?t-1:t])}let g=0;if(void 0!==f&&(g=o.indexOf(f),g<0&&(g=i.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(g=g-i.slidesPerViewDynamic("previous",!0)+1,g=Math.max(g,0))),n.rewind&&i.isBeginning){const n=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(n,t,e,s)}return n.loop&&0===i.activeIndex&&n.cssMode?(requestAnimationFrame(()=>{i.slideTo(g,t,e,s)}),!0):i.slideTo(g,t,e,s)},slideReset:function(t,e,s){void 0===e&&(e=!0);const i=this;if(!i.destroyed)return void 0===t&&(t=i.params.speed),i.slideTo(i.activeIndex,t,e,s)},slideToClosest:function(t,e,s,i){void 0===e&&(e=!0),void 0===i&&(i=.5);const n=this;if(n.destroyed)return;void 0===t&&(t=n.params.speed);let r=n.activeIndex;const o=Math.min(n.params.slidesPerGroupSkip,r),a=o+Math.floor((r-o)/n.params.slidesPerGroup),l=n.rtlTranslate?n.translate:-n.translate;if(l>=n.snapGrid[a]){const t=n.snapGrid[a];l-t>(n.snapGrid[a+1]-t)*i&&(r+=n.params.slidesPerGroup)}else{const t=n.snapGrid[a-1];l-t<=(n.snapGrid[a]-t)*i&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,t,e,s)},slideToClickedSlide:function(){const t=this;if(t.destroyed)return;const{params:e,slidesEl:s}=t,i="auto"===e.slidesPerView?t.slidesPerViewDynamic():e.slidesPerView;let n,r=t.getSlideIndexWhenGrid(t.clickedIndex);const o=t.isElement?"swiper-slide":`.${e.slideClass}`,a=t.grid&&t.params.grid&&t.params.grid.rows>1;if(e.loop){if(t.animating)return;n=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?t.slideToLoop(n):r>(a?(t.slides.length-i)/2-(t.params.grid.rows-1):t.slides.length-i)?(t.loopFix(),r=t.getSlideIndex(T(s,`${o}[data-swiper-slide-index="${n}"]`)[0]),h(()=>{t.slideTo(r)})):t.slideTo(r)}else t.slideTo(r)}},loop:{loopCreate:function(t,e){const s=this,{params:i,slidesEl:n}=s;if(!i.loop||s.virtual&&s.params.virtual.enabled)return;const r=()=>{T(n,`.${i.slideClass}, swiper-slide`).forEach((t,e)=>{t.setAttribute("data-swiper-slide-index",e)})},o=s.grid&&i.grid&&i.grid.rows>1;i.loopAddBlankSlides&&(i.slidesPerGroup>1||o)&&(()=>{const t=T(n,`.${i.slideBlankClass}`);t.forEach(t=>{t.remove()}),t.length>0&&(s.recalcSlides(),s.updateSlides())})();const a=i.slidesPerGroup*(o?i.grid.rows:1),l=s.slides.length%a!==0,c=o&&s.slides.length%i.grid.rows!==0,d=t=>{for(let e=0;e<t;e+=1){const t=s.isElement?b("swiper-slide",[i.slideBlankClass]):b("div",[i.slideClass,i.slideBlankClass]);s.slidesEl.append(t)}};if(l){if(i.loopAddBlankSlides){d(a-s.slides.length%a),s.recalcSlides(),s.updateSlides()}else S();r()}else if(c){if(i.loopAddBlankSlides){d(i.grid.rows-s.slides.length%i.grid.rows),s.recalcSlides(),s.updateSlides()}else S();r()}else r();s.loopFix({slideRealIndex:t,direction:i.centeredSlides?void 0:"next",initial:e})},loopFix:function(t){let{slideRealIndex:e,slideTo:s=!0,direction:i,setTranslate:n,activeSlideIndex:r,initial:o,byController:a,byMousewheel:l}=void 0===t?{}:t;const c=this;if(!c.params.loop)return;c.emit("beforeLoopFix");const{slides:d,allowSlidePrev:u,allowSlideNext:h,slidesEl:p,params:m}=c,{centeredSlides:f,initialSlide:g}=m;if(c.allowSlidePrev=!0,c.allowSlideNext=!0,c.virtual&&m.virtual.enabled)return s&&(m.centeredSlides||0!==c.snapIndex?m.centeredSlides&&c.snapIndex<m.slidesPerView?c.slideTo(c.virtual.slides.length+c.snapIndex,0,!1,!0):c.snapIndex===c.snapGrid.length-1&&c.slideTo(c.virtual.slidesBefore,0,!1,!0):c.slideTo(c.virtual.slides.length,0,!1,!0)),c.allowSlidePrev=u,c.allowSlideNext=h,void c.emit("loopFix");let v=m.slidesPerView;"auto"===v?v=c.slidesPerViewDynamic():(v=Math.ceil(parseFloat(m.slidesPerView,10)),f&&v%2==0&&(v+=1));const y=m.slidesPerGroupAuto?v:m.slidesPerGroup;let x=f?Math.max(y,Math.ceil(v/2)):y;x%y!==0&&(x+=y-x%y),x+=m.loopAdditionalSlides,c.loopedSlides=x;const w=c.grid&&m.grid&&m.grid.rows>1;(d.length<v+x||"cards"===c.params.effect&&d.length<v+2*x||w&&"row"===m.grid.fill)&&S();const T=[],b=[],P=w?Math.ceil(d.length/m.grid.rows):d.length,E=o&&P-g<v&&!f;let M=E?g:c.activeIndex;void 0===r?r=c.getSlideIndex(d.find(t=>t.classList.contains(m.slideActiveClass))):M=r;const C="next"===i||!i,A="prev"===i||!i;let k=0,V=0;const L=(w?d[r].column:r)+(f&&void 0===n?-v/2+.5:0);if(L<x){k=Math.max(x-L,y);for(let t=0;t<x-L;t+=1){const e=t-Math.floor(t/P)*P;if(w){const t=P-e-1;for(let e=d.length-1;e>=0;e-=1)d[e].column===t&&T.push(e)}else T.push(P-e-1)}}else if(L+v>P-x){V=Math.max(L-(P-2*x),y),E&&(V=Math.max(V,v-P+g+1));for(let t=0;t<V;t+=1){const e=t-Math.floor(t/P)*P;w?d.forEach((t,s)=>{t.column===e&&b.push(s)}):b.push(e)}}if(c.__preventObserver__=!0,requestAnimationFrame(()=>{c.__preventObserver__=!1}),"cards"===c.params.effect&&d.length<v+2*x&&(b.includes(r)&&b.splice(b.indexOf(r),1),T.includes(r)&&T.splice(T.indexOf(r),1)),A&&T.forEach(t=>{d[t].swiperLoopMoveDOM=!0,p.prepend(d[t]),d[t].swiperLoopMoveDOM=!1}),C&&b.forEach(t=>{d[t].swiperLoopMoveDOM=!0,p.append(d[t]),d[t].swiperLoopMoveDOM=!1}),c.recalcSlides(),"auto"===m.slidesPerView?c.updateSlides():w&&(T.length>0&&A||b.length>0&&C)&&c.slides.forEach((t,e)=>{c.grid.updateSlide(e,t,c.slides)}),m.watchSlidesProgress&&c.updateSlidesOffset(),s)if(T.length>0&&A){if(void 0===e){const t=c.slidesGrid[M],e=c.slidesGrid[M+k]-t;l?c.setTranslate(c.translate-e):(c.slideTo(M+Math.ceil(k),0,!1,!0),n&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-e,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-e))}else if(n){const t=w?T.length/m.grid.rows:T.length;c.slideTo(c.activeIndex+t,0,!1,!0),c.touchEventsData.currentTranslate=c.translate}}else if(b.length>0&&C)if(void 0===e){const t=c.slidesGrid[M],e=c.slidesGrid[M-V]-t;l?c.setTranslate(c.translate-e):(c.slideTo(M-V,0,!1,!0),n&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-e,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-e))}else{const t=w?b.length/m.grid.rows:b.length;c.slideTo(c.activeIndex-t,0,!1,!0)}if(c.allowSlidePrev=u,c.allowSlideNext=h,c.controller&&c.controller.control&&!a){const t={slideRealIndex:e,direction:i,setTranslate:n,activeSlideIndex:r,byController:!0};Array.isArray(c.controller.control)?c.controller.control.forEach(e=>{!e.destroyed&&e.params.loop&&e.loopFix({...t,slideTo:e.params.slidesPerView===m.slidesPerView&&s})}):c.controller.control instanceof c.constructor&&c.controller.control.params.loop&&c.controller.control.loopFix({...t,slideTo:c.controller.control.params.slidesPerView===m.slidesPerView&&s})}c.emit("loopFix")},loopDestroy:function(){const t=this,{params:e,slidesEl:s}=t;if(!e.loop||!s||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const i=[];t.slides.forEach(t=>{const e=void 0===t.swiperSlideIndex?1*t.getAttribute("data-swiper-slide-index"):t.swiperSlideIndex;i[e]=t}),t.slides.forEach(t=>{t.removeAttribute("data-swiper-slide-index")}),i.forEach(t=>{s.append(t)}),t.recalcSlides(),t.slideTo(t.realIndex,0)}},grabCursor:{setGrabCursor:function(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const s="container"===e.params.touchEventsTarget?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),s.style.cursor="move",s.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})},unsetGrabCursor:function(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t["container"===t.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1}))}},events:{attachEvents:function(){const t=this,{params:e}=t;t.onTouchStart=W.bind(t),t.onTouchMove=U.bind(t),t.onTouchEnd=H.bind(t),t.onDocumentTouchStart=Z.bind(t),e.cssMode&&(t.onScroll=q.bind(t)),t.onClick=Y.bind(t),t.onLoad=K.bind(t),J(t,"on")},detachEvents:function(){J(this,"off")}},breakpoints:{setBreakpoint:function(){const t=this,{realIndex:e,initialized:s,params:i,el:n}=t,r=i.breakpoints;if(!r||r&&0===Object.keys(r).length)return;const o=c(),a="window"!==i.breakpointsBase&&i.breakpointsBase?"container":i.breakpointsBase,l=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?t.el:o.querySelector(i.breakpointsBase),d=t.getBreakpoint(r,a,l);if(!d||t.currentBreakpoint===d)return;const u=(d in r?r[d]:void 0)||t.originalParams,h=Q(t,i),p=Q(t,u),m=t.params.grabCursor,f=u.grabCursor,g=i.enabled;h&&!p?(n.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),t.emitContainerClasses()):!h&&p&&(n.classList.add(`${i.containerModifierClass}grid`),(u.grid.fill&&"column"===u.grid.fill||!u.grid.fill&&"column"===i.grid.fill)&&n.classList.add(`${i.containerModifierClass}grid-column`),t.emitContainerClasses()),m&&!f?t.unsetGrabCursor():!m&&f&&t.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(e=>{if(void 0===u[e])return;const s=i[e]&&i[e].enabled,n=u[e]&&u[e].enabled;s&&!n&&t[e].disable(),!s&&n&&t[e].enable()});const y=u.direction&&u.direction!==i.direction,x=i.loop&&(u.slidesPerView!==i.slidesPerView||y),w=i.loop;y&&s&&t.changeDirection(),v(t.params,u);const T=t.params.enabled,S=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),g&&!T?t.disable():!g&&T&&t.enable(),t.currentBreakpoint=d,t.emit("_beforeBreakpoint",u),s&&(x?(t.loopDestroy(),t.loopCreate(e),t.updateSlides()):!w&&S?(t.loopCreate(e),t.updateSlides()):w&&!S&&t.loopDestroy()),t.emit("breakpoint",u)},getBreakpoint:function(t,e,s){if(void 0===e&&(e="window"),!t||"container"===e&&!s)return;let i=!1;const n=u(),r="window"===e?n.innerHeight:s.clientHeight,o=Object.keys(t).map(t=>{if("string"==typeof t&&0===t.indexOf("@")){const e=parseFloat(t.substr(1));return{value:r*e,point:t}}return{value:t,point:t}});o.sort((t,e)=>parseInt(t.value,10)-parseInt(e.value,10));for(let a=0;a<o.length;a+=1){const{point:t,value:r}=o[a];"window"===e?n.matchMedia(`(min-width: ${r}px)`).matches&&(i=t):r<=s.clientWidth&&(i=t)}return i||"max"}},checkOverflow:{checkOverflow:function(){const t=this,{isLocked:e,params:s}=t,{slidesOffsetBefore:i}=s;if(i){const e=t.slides.length-1,s=t.slidesGrid[e]+t.slidesSizesGrid[e]+2*i;t.isLocked=t.size>s}else t.isLocked=1===t.snapGrid.length;!0===s.allowSlideNext&&(t.allowSlideNext=!t.isLocked),!0===s.allowSlidePrev&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}},classes:{addClasses:function(){const t=this,{classNames:e,params:s,rtl:i,el:n,device:r}=t,o=function(t,e){const s=[];return t.forEach(t=>{"object"==typeof t?Object.keys(t).forEach(i=>{t[i]&&s.push(e+i)}):"string"==typeof t&&s.push(e+t)}),s}(["initialized",s.direction,{"free-mode":t.params.freeMode&&s.freeMode.enabled},{autoheight:s.autoHeight},{rtl:i},{grid:s.grid&&s.grid.rows>1},{"grid-column":s.grid&&s.grid.rows>1&&"column"===s.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":s.cssMode},{centered:s.cssMode&&s.centeredSlides},{"watch-progress":s.watchSlidesProgress}],s.containerModifierClass);e.push(...o),n.classList.add(...e),t.emitContainerClasses()},removeClasses:function(){const{el:t,classNames:e}=this;t&&"string"!=typeof t&&(t.classList.remove(...e),this.emitContainerClasses())}}},it={};class nt{constructor(){let t,e;for(var s=arguments.length,i=new Array(s),n=0;n<s;n++)i[n]=arguments[n];1===i.length&&i[0].constructor&&"Object"===Object.prototype.toString.call(i[0]).slice(8,-1)?e=i[0]:[t,e]=i,e||(e={}),e=v({},e),t&&!e.el&&(e.el=t);const r=c();if(e.el&&"string"==typeof e.el&&r.querySelectorAll(e.el).length>1){const t=[];return r.querySelectorAll(e.el).forEach(s=>{const i=v({},e,{el:s});t.push(new nt(i))}),t}const o=this;o.__swiper__=!0,o.support=O(),o.device=R({userAgent:e.userAgent}),o.browser=B(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],e.modules&&Array.isArray(e.modules)&&o.modules.push(...e.modules);const a={};o.modules.forEach(t=>{t({params:e,swiper:o,extendParams:et(e,a),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})});const l=v({},tt,a);return o.params=v({},l,it,e),o.originalParams=v({},o.params),o.passedParams=v({},e),o.params&&o.params.on&&Object.keys(o.params.on).forEach(t=>{o.on(t,o.params.on[t])}),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===o.params.direction,isVertical:()=>"vertical"===o.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:e,params:s}=this,i=E(T(e,`.${s.slideClass}, swiper-slide`)[0]);return E(t)-i}getSlideIndexByData(t){return this.getSlideIndex(this.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t))}getSlideIndexWhenGrid(t){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?t=Math.floor(t/this.params.grid.rows):"row"===this.params.grid.fill&&(t%=Math.ceil(this.slides.length/this.params.grid.rows))),t}recalcSlides(){const{slidesEl:t,params:e}=this;this.slides=T(t,`.${e.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,e){const s=this;t=Math.min(Math.max(t,0),1);const i=s.minTranslate(),n=(s.maxTranslate()-i)*t+i;s.translateTo(n,void 0===e?0:e),s.updateActiveIndex(),s.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const e=t.el.className.split(" ").filter(e=>0===e.indexOf("swiper")||0===e.indexOf(t.params.containerModifierClass));t.emit("_containerClasses",e.join(" "))}getSlideClasses(t){const e=this;return e.destroyed?"":t.className.split(" ").filter(t=>0===t.indexOf("swiper-slide")||0===t.indexOf(e.params.slideClass)).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const e=[];t.slides.forEach(s=>{const i=t.getSlideClasses(s);e.push({slideEl:s,classNames:i}),t.emit("_slideClass",s,i)}),t.emit("_slideClasses",e)}slidesPerViewDynamic(t,e){void 0===t&&(t="current"),void 0===e&&(e=!1);const{params:s,slides:i,slidesGrid:n,slidesSizesGrid:r,size:o,activeIndex:a}=this;let l=1;if("number"==typeof s.slidesPerView)return s.slidesPerView;if(s.centeredSlides){let t,e=i[a]?Math.ceil(i[a].swiperSlideSize):0;for(let s=a+1;s<i.length;s+=1)i[s]&&!t&&(e+=Math.ceil(i[s].swiperSlideSize),l+=1,e>o&&(t=!0));for(let s=a-1;s>=0;s-=1)i[s]&&!t&&(e+=i[s].swiperSlideSize,l+=1,e>o&&(t=!0))}else if("current"===t)for(let c=a+1;c<i.length;c+=1){(e?n[c]+r[c]-n[a]<o:n[c]-n[a]<o)&&(l+=1)}else for(let c=a-1;c>=0;c-=1){n[a]-n[c]<o&&(l+=1)}return l}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:e,params:s}=t;function i(){const e=t.rtlTranslate?-1*t.translate:t.translate,s=Math.min(Math.max(e,t.maxTranslate()),t.minTranslate());t.setTranslate(s),t.updateActiveIndex(),t.updateSlidesClasses()}let n;if(s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&z(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)i(),s.autoHeight&&t.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){const e=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;n=t.slideTo(e.length-1,0,!1,!0)}else n=t.slideTo(t.activeIndex,0,!1,!0);n||i()}s.watchOverflow&&e!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,e){void 0===e&&(e=!0);const s=this,i=s.params.direction;return t||(t="horizontal"===i?"vertical":"horizontal"),t===i||"horizontal"!==t&&"vertical"!==t||(s.el.classList.remove(`${s.params.containerModifierClass}${i}`),s.el.classList.add(`${s.params.containerModifierClass}${t}`),s.emitContainerClasses(),s.params.direction=t,s.slides.forEach(e=>{"vertical"===t?e.style.width="":e.style.height=""}),s.emit("changeDirection"),e&&s.update()),s}changeLanguageDirection(t){const e=this;e.rtl&&"rtl"===t||!e.rtl&&"ltr"===t||(e.rtl="rtl"===t,e.rtlTranslate="horizontal"===e.params.direction&&e.rtl,e.rtl?(e.el.classList.add(`${e.params.containerModifierClass}rtl`),e.el.dir="rtl"):(e.el.classList.remove(`${e.params.containerModifierClass}rtl`),e.el.dir="ltr"),e.update())}mount(t){const e=this;if(e.mounted)return!0;let s=t||e.params.el;if("string"==typeof s&&(s=document.querySelector(s)),!s)return!1;s.swiper=e,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===e.params.swiperElementNodeName.toUpperCase()&&(e.isElement=!0);const i=()=>`.${(e.params.wrapperClass||"").trim().split(" ").join(".")}`;let n=(()=>{if(s&&s.shadowRoot&&s.shadowRoot.querySelector){return s.shadowRoot.querySelector(i())}return T(s,i())[0]})();return!n&&e.params.createElements&&(n=b("div",e.params.wrapperClass),s.append(n),T(s,`.${e.params.slideClass}`).forEach(t=>{n.append(t)})),Object.assign(e,{el:s,wrapperEl:n,slidesEl:e.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:n,hostEl:e.isElement?s.parentNode.host:s,mounted:!0,rtl:"rtl"===s.dir.toLowerCase()||"rtl"===P(s,"direction"),rtlTranslate:"horizontal"===e.params.direction&&("rtl"===s.dir.toLowerCase()||"rtl"===P(s,"direction")),wrongRTL:"-webkit-box"===P(n,"display")}),!0}init(t){const e=this;if(e.initialized)return e;if(!1===e.mount(t))return e;e.emit("beforeInit"),e.params.breakpoints&&e.setBreakpoint(),e.addClasses(),e.updateSize(),e.updateSlides(),e.params.watchOverflow&&e.checkOverflow(),e.params.grabCursor&&e.enabled&&e.setGrabCursor(),e.params.loop&&e.virtual&&e.params.virtual.enabled?e.slideTo(e.params.initialSlide+e.virtual.slidesBefore,0,e.params.runCallbacksOnInit,!1,!0):e.slideTo(e.params.initialSlide,0,e.params.runCallbacksOnInit,!1,!0),e.params.loop&&e.loopCreate(void 0,!0),e.attachEvents();const s=[...e.el.querySelectorAll('[loading="lazy"]')];return e.isElement&&s.push(...e.hostEl.querySelectorAll('[loading="lazy"]')),s.forEach(t=>{t.complete?z(e,t):t.addEventListener("load",t=>{z(e,t.target)})}),G(e),e.initialized=!0,G(e),e.emit("init"),e.emit("afterInit"),e}destroy(t,e){void 0===t&&(t=!0),void 0===e&&(e=!0);const s=this,{params:i,el:n,wrapperEl:r,slides:o}=s;return void 0===s.params||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),i.loop&&s.loopDestroy(),e&&(s.removeClasses(),n&&"string"!=typeof n&&n.removeAttribute("style"),r&&r.removeAttribute("style"),o&&o.length&&o.forEach(t=>{t.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),t.removeAttribute("style"),t.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(t=>{s.off(t)}),!1!==t&&(s.el&&"string"!=typeof s.el&&(s.el.swiper=null),function(t){const e=t;Object.keys(e).forEach(t=>{try{e[t]=null}catch(s){}try{delete e[t]}catch(s){}})}(s)),s.destroyed=!0),null}static extendDefaults(t){v(it,t)}static get extendedDefaults(){return it}static get defaults(){return tt}static installModule(t){nt.prototype.__modules__||(nt.prototype.__modules__=[]);const e=nt.prototype.__modules__;"function"==typeof t&&e.indexOf(t)<0&&e.push(t)}static use(t){return Array.isArray(t)?(t.forEach(t=>nt.installModule(t)),nt):(nt.installModule(t),nt)}}Object.keys(st).forEach(t=>{Object.keys(st[t]).forEach(e=>{nt.prototype[e]=st[t][e]})}),nt.use([function(t){let{swiper:e,on:s,emit:i}=t;const n=u();let r=null,o=null;const a=()=>{e&&!e.destroyed&&e.initialized&&(i("beforeResize"),i("resize"))},l=()=>{e&&!e.destroyed&&e.initialized&&i("orientationchange")};s("init",()=>{e.params.resizeObserver&&void 0!==n.ResizeObserver?e&&!e.destroyed&&e.initialized&&(r=new ResizeObserver(t=>{o=n.requestAnimationFrame(()=>{const{width:s,height:i}=e;let n=s,r=i;t.forEach(t=>{let{contentBoxSize:s,contentRect:i,target:o}=t;o&&o!==e.el||(n=i?i.width:(s[0]||s).inlineSize,r=i?i.height:(s[0]||s).blockSize)}),n===s&&r===i||a()})}),r.observe(e.el)):(n.addEventListener("resize",a),n.addEventListener("orientationchange",l))}),s("destroy",()=>{o&&n.cancelAnimationFrame(o),r&&r.unobserve&&e.el&&(r.unobserve(e.el),r=null),n.removeEventListener("resize",a),n.removeEventListener("orientationchange",l)})},function(t){let{swiper:e,extendParams:s,on:i,emit:n}=t;const r=[],o=u(),a=function(t,s){void 0===s&&(s={});const i=new(o.MutationObserver||o.WebkitMutationObserver)(t=>{if(e.__preventObserver__)return;if(1===t.length)return void n("observerUpdate",t[0]);const s=function(){n("observerUpdate",t[0])};o.requestAnimationFrame?o.requestAnimationFrame(s):o.setTimeout(s,0)});i.observe(t,{attributes:void 0===s.attributes||s.attributes,childList:e.isElement||(void 0===s.childList||s).childList,characterData:void 0===s.characterData||s.characterData}),r.push(i)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",()=>{if(e.params.observer){if(e.params.observeParents){const t=M(e.hostEl);for(let e=0;e<t.length;e+=1)a(t[e])}a(e.hostEl,{childList:e.params.observeSlideChildren}),a(e.wrapperEl,{attributes:!1})}}),i("destroy",()=>{r.forEach(t=>{t.disconnect()}),r.splice(0,r.length)})}]);const rt=t.createContext({});function ot(e){const s=t.useRef(null);return null===s.current&&(s.current=e()),s.current}const at="undefined"!=typeof window,lt=at?t.useLayoutEffect:t.useEffect,ct=t.createContext(null);function dt(t,e){-1===t.indexOf(e)&&t.push(e)}function ut(t,e){const s=t.indexOf(e);s>-1&&t.splice(s,1)}const ht=(t,e,s)=>s>e?e:s<t?t:s;const pt={},mt=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function ft(t){return"object"==typeof t&&null!==t}const gt=t=>/^0[^.\s]+$/u.test(t);function vt(t){let e;return()=>(void 0===e&&(e=t()),e)}const yt=t=>t,xt=(t,e)=>s=>e(t(s)),wt=(...t)=>t.reduce(xt),Tt=(t,e,s)=>{const i=e-t;return 0===i?1:(s-t)/i};class St{constructor(){this.subscriptions=[]}add(t){return dt(this.subscriptions,t),()=>ut(this.subscriptions,t)}notify(t,e,s){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,s);else for(let n=0;n<i;n++){const i=this.subscriptions[n];i&&i(t,e,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const bt=t=>1e3*t,Pt=t=>t/1e3;function Et(t,e){return e?t*(1e3/e):0}const Mt=(t,e,s)=>(((1-3*s+3*e)*t+(3*s-6*e))*t+3*e)*t;function Ct(t,e,s,i){if(t===e&&s===i)return yt;const n=e=>function(t,e,s,i,n){let r,o,a=0;do{o=e+(s-e)/2,r=Mt(o,i,n)-t,r>0?s=o:e=o}while(Math.abs(r)>1e-7&&++a<12);return o}(e,0,1,t,s);return t=>0===t||1===t?t:Mt(n(t),e,i)}const At=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,kt=t=>e=>1-t(1-e),Vt=Ct(.33,1.53,.69,.99),Lt=kt(Vt),Dt=At(Lt),It=t=>(t*=2)<1?.5*Lt(t):.5*(2-Math.pow(2,-10*(t-1))),Ot=t=>1-Math.sin(Math.acos(t)),Rt=kt(Ot),Bt=At(Ot),Ft=Ct(.42,0,1,1),jt=Ct(0,0,.58,1),zt=Ct(.42,0,.58,1),Nt=t=>Array.isArray(t)&&"number"==typeof t[0],Gt={linear:yt,easeIn:Ft,easeInOut:zt,easeOut:jt,circIn:Ot,circInOut:Bt,circOut:Rt,backIn:Lt,backInOut:Dt,backOut:Vt,anticipate:It},$t=t=>{if(Nt(t)){t.length;const[e,s,i,n]=t;return Ct(e,s,i,n)}return"string"==typeof t?Gt[t]:t},_t=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Wt={value:null};function Ut(t,e){let s=!1,i=!0;const n={delta:0,timestamp:0,isProcessing:!1},r=()=>s=!0,o=_t.reduce((t,s)=>(t[s]=function(t,e){let s=new Set,i=new Set,n=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(e){o.has(e)&&(d.schedule(e),t()),l++,e(a)}const d={schedule:(t,e=!1,r=!1)=>{const a=r&&n?s:i;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),o.delete(t)},process:t=>{a=t,n?r=!0:(n=!0,[s,i]=[i,s],s.forEach(c),e&&Wt.value&&Wt.value.frameloop[e].push(l),l=0,s.clear(),n=!1,r&&(r=!1,d.process(t)))}};return d}(r,e?s:void 0),t),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:d,update:u,preRender:h,render:p,postRender:m}=o,f=()=>{const r=pt.useManualTiming?n.timestamp:performance.now();s=!1,pt.useManualTiming||(n.delta=i?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),c.process(n),d.process(n),u.process(n),h.process(n),p.process(n),m.process(n),n.isProcessing=!1,s&&e&&(i=!1,t(f))};return{schedule:_t.reduce((e,r)=>{const a=o[r];return e[r]=(e,r=!1,o=!1)=>(s||(s=!0,i=!0,n.isProcessing||t(f)),a.schedule(e,r,o)),e},{}),cancel:t=>{for(let e=0;e<_t.length;e++)o[_t[e]].cancel(t)},state:n,steps:o}}const{schedule:Ht,cancel:Xt,state:Yt,steps:qt}=Ut("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:yt,!0);let Kt;function Zt(){Kt=void 0}const Jt={now:()=>(void 0===Kt&&Jt.set(Yt.isProcessing||pt.useManualTiming?Yt.timestamp:performance.now()),Kt),set:t=>{Kt=t,queueMicrotask(Zt)}},Qt=t=>e=>"string"==typeof e&&e.startsWith(t),te=Qt("--"),ee=Qt("var(--"),se=t=>!!ee(t)&&ie.test(t.split("/*")[0].trim()),ie=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ne={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},re={...ne,transform:t=>ht(0,1,t)},oe={...ne,default:1},ae=t=>Math.round(1e5*t)/1e5,le=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ce=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,de=(t,e)=>s=>Boolean("string"==typeof s&&ce.test(s)&&s.startsWith(t)||e&&!function(t){return null==t}(s)&&Object.prototype.hasOwnProperty.call(s,e)),ue=(t,e,s)=>i=>{if("string"!=typeof i)return i;const[n,r,o,a]=i.match(le);return{[t]:parseFloat(n),[e]:parseFloat(r),[s]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},he={...ne,transform:t=>Math.round((t=>ht(0,255,t))(t))},pe={test:de("rgb","red"),parse:ue("red","green","blue"),transform:({red:t,green:e,blue:s,alpha:i=1})=>"rgba("+he.transform(t)+", "+he.transform(e)+", "+he.transform(s)+", "+ae(re.transform(i))+")"};const me={test:de("#"),parse:function(t){let e="",s="",i="",n="";return t.length>5?(e=t.substring(1,3),s=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),s=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,s+=s,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(s,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:pe.transform},fe=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ge=fe("deg"),ve=fe("%"),ye=fe("px"),xe=fe("vh"),we=fe("vw"),Te=(()=>({...ve,parse:t=>ve.parse(t)/100,transform:t=>ve.transform(100*t)}))(),Se={test:de("hsl","hue"),parse:ue("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:s,alpha:i=1})=>"hsla("+Math.round(t)+", "+ve.transform(ae(e))+", "+ve.transform(ae(s))+", "+ae(re.transform(i))+")"},be={test:t=>pe.test(t)||me.test(t)||Se.test(t),parse:t=>pe.test(t)?pe.parse(t):Se.test(t)?Se.parse(t):me.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?pe.transform(t):Se.transform(t),getAnimatableNone:t=>{const e=be.parse(t);return e.alpha=0,be.transform(e)}},Pe=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Ee="number",Me="color",Ce=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ae(t){const e=t.toString(),s=[],i={color:[],number:[],var:[]},n=[];let r=0;const o=e.replace(Ce,t=>(be.test(t)?(i.color.push(r),n.push(Me),s.push(be.parse(t))):t.startsWith("var(")?(i.var.push(r),n.push("var"),s.push(t)):(i.number.push(r),n.push(Ee),s.push(parseFloat(t))),++r,"${}")).split("${}");return{values:s,split:o,indexes:i,types:n}}function ke(t){return Ae(t).values}function Ve(t){const{split:e,types:s}=Ae(t),i=e.length;return t=>{let n="";for(let r=0;r<i;r++)if(n+=e[r],void 0!==t[r]){const e=s[r];n+=e===Ee?ae(t[r]):e===Me?be.transform(t[r]):t[r]}return n}}const Le=t=>"number"==typeof t?0:be.test(t)?be.getAnimatableNone(t):t;const De={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(le)?.length||0)+(t.match(Pe)?.length||0)>0},parse:ke,createTransformer:Ve,getAnimatableNone:function(t){const e=ke(t);return Ve(t)(e.map(Le))}};function Ie(t,e,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+6*(e-t)*s:s<.5?e:s<2/3?t+(e-t)*(2/3-s)*6:t}function Oe(t,e){return s=>s>0?e:t}const Re=(t,e,s)=>t+(e-t)*s,Be=(t,e,s)=>{const i=t*t,n=s*(e*e-i)+i;return n<0?0:Math.sqrt(n)},Fe=[me,pe,Se];function je(t){const e=(s=t,Fe.find(t=>t.test(s)));var s;if(!Boolean(e))return!1;let i=e.parse(t);return e===Se&&(i=function({hue:t,saturation:e,lightness:s,alpha:i}){t/=360,s/=100;let n=0,r=0,o=0;if(e/=100){const i=s<.5?s*(1+e):s+e-s*e,a=2*s-i;n=Ie(a,i,t+1/3),r=Ie(a,i,t),o=Ie(a,i,t-1/3)}else n=r=o=s;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:i}}(i)),i}const ze=(t,e)=>{const s=je(t),i=je(e);if(!s||!i)return Oe(t,e);const n={...s};return t=>(n.red=Be(s.red,i.red,t),n.green=Be(s.green,i.green,t),n.blue=Be(s.blue,i.blue,t),n.alpha=Re(s.alpha,i.alpha,t),pe.transform(n))},Ne=new Set(["none","hidden"]);function Ge(t,e){return s=>Re(t,e,s)}function $e(t){return"number"==typeof t?Ge:"string"==typeof t?se(t)?Oe:be.test(t)?ze:Ue:Array.isArray(t)?_e:"object"==typeof t?be.test(t)?ze:We:Oe}function _e(t,e){const s=[...t],i=s.length,n=t.map((t,s)=>$e(t)(t,e[s]));return t=>{for(let e=0;e<i;e++)s[e]=n[e](t);return s}}function We(t,e){const s={...t,...e},i={};for(const n in s)void 0!==t[n]&&void 0!==e[n]&&(i[n]=$e(t[n])(t[n],e[n]));return t=>{for(const e in i)s[e]=i[e](t);return s}}const Ue=(t,e)=>{const s=De.createTransformer(e),i=Ae(t),n=Ae(e);return i.indexes.var.length===n.indexes.var.length&&i.indexes.color.length===n.indexes.color.length&&i.indexes.number.length>=n.indexes.number.length?Ne.has(t)&&!n.values.length||Ne.has(e)&&!i.values.length?function(t,e){return Ne.has(t)?s=>s<=0?t:e:s=>s>=1?e:t}(t,e):wt(_e(function(t,e){const s=[],i={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){const r=e.types[n],o=t.indexes[r][i[r]],a=t.values[o]??0;s[n]=a,i[r]++}return s}(i,n),n.values),s):Oe(t,e)};function He(t,e,s){if("number"==typeof t&&"number"==typeof e&&"number"==typeof s)return Re(t,e,s);return $e(t)(t,e)}const Xe=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>Ht.update(e,t),stop:()=>Xt(e),now:()=>Yt.isProcessing?Yt.timestamp:Jt.now()}},Ye=(t,e,s=10)=>{let i="";const n=Math.max(Math.round(e/s),2);for(let r=0;r<n;r++)i+=Math.round(1e4*t(r/(n-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},qe=2e4;function Ke(t){let e=0;let s=t.next(e);for(;!s.done&&e<qe;)e+=50,s=t.next(e);return e>=qe?1/0:e}function Ze(t,e,s){const i=Math.max(e-5,0);return Et(s-t(i),e-i)}const Je=100,Qe=10,ts=1,es=0,ss=800,is=.3,ns=.3,rs={granular:.01,default:2},os={granular:.005,default:.5},as=.01,ls=10,cs=.05,ds=1,us=.001;function hs({duration:t=ss,bounce:e=is,velocity:s=es,mass:i=ts}){let n,r,o=1-e;o=ht(cs,ds,o),t=ht(as,ls,Pt(t)),o<1?(n=e=>{const i=e*o,n=i*t,r=i-s,a=ms(e,o),l=Math.exp(-n);return us-r/a*l},r=e=>{const i=e*o*t,r=i*s+s,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-i),c=ms(Math.pow(e,2),o);return(-n(e)+us>0?-1:1)*((r-a)*l)/c}):(n=e=>Math.exp(-e*t)*((e-s)*t+1)-.001,r=e=>Math.exp(-e*t)*(t*t*(s-e)));const a=function(t,e,s){let i=s;for(let n=1;n<ps;n++)i-=t(i)/e(i);return i}(n,r,5/t);if(t=bt(t),isNaN(a))return{stiffness:Je,damping:Qe,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}const ps=12;function ms(t,e){return t*Math.sqrt(1-e*e)}const fs=["duration","bounce"],gs=["stiffness","damping","mass"];function vs(t,e){return e.some(e=>void 0!==t[e])}function ys(t=ns,e=is){const s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:n}=s;const r=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],a={done:!1,value:r},{stiffness:l,damping:c,mass:d,duration:u,velocity:h,isResolvedFromDuration:p}=function(t){let e={velocity:es,stiffness:Je,damping:Qe,mass:ts,isResolvedFromDuration:!1,...t};if(!vs(t,gs)&&vs(t,fs))if(t.visualDuration){const s=t.visualDuration,i=2*Math.PI/(1.2*s),n=i*i,r=2*ht(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:ts,stiffness:n,damping:r}}else{const s=hs(t);e={...e,...s,mass:ts},e.isResolvedFromDuration=!0}return e}({...s,velocity:-Pt(s.velocity||0)}),m=h||0,f=c/(2*Math.sqrt(l*d)),g=o-r,v=Pt(Math.sqrt(l/d)),y=Math.abs(g)<5;let x;if(i||(i=y?rs.granular:rs.default),n||(n=y?os.granular:os.default),f<1){const t=ms(v,f);x=e=>{const s=Math.exp(-f*v*e);return o-s*((m+f*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>o-Math.exp(-v*t)*(g+(m+v*g)*t);else{const t=v*Math.sqrt(f*f-1);x=e=>{const s=Math.exp(-f*v*e),i=Math.min(t*e,300);return o-s*((m+f*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&u||null,next:t=>{const e=x(t);if(p)a.done=t>=u;else{let s=0===t?m:0;f<1&&(s=0===t?bt(m):Ze(x,t,e));const r=Math.abs(s)<=i,l=Math.abs(o-e)<=n;a.done=r&&l}return a.value=a.done?o:e,a},toString:()=>{const t=Math.min(Ke(w),qe),e=Ye(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function xs({keyframes:t,velocity:e=0,power:s=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:d}){const u=t[0],h={done:!1,value:u},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=s*e;const f=u+m,g=void 0===o?f:o(f);g!==f&&(m=g-u);const v=t=>-m*Math.exp(-t/i),y=t=>g+v(t),x=t=>{const e=v(t),s=y(t);h.done=Math.abs(e)<=c,h.value=h.done?g:s};let w,T;const S=t=>{var e;(e=h.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,T=ys({keyframes:[h.value,p(h.value)],velocity:Ze(y,t,h.value),damping:n,stiffness:r,restDelta:c,restSpeed:d}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,x(t),S(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&x(t),h)}}}function ws(t,e,{clamp:s=!0,ease:i,mixer:n}={}){const r=t.length;if(e.length,1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];const o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,s){const i=[],n=s||pt.mix||He,r=t.length-1;for(let o=0;o<r;o++){let s=n(t[o],t[o+1]);if(e){const t=Array.isArray(e)?e[o]||yt:e;s=wt(t,s)}i.push(s)}return i}(e,i,n),l=a.length,c=s=>{if(o&&s<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(s<t[i+1]);i++);const n=Tt(t[i],t[i+1],s);return a[i](n)};return s?e=>c(ht(t[0],t[r-1],e)):c}function Ts(t){const e=[0];return function(t,e){const s=t[t.length-1];for(let i=1;i<=e;i++){const n=Tt(0,e,i);t.push(Re(s,1,n))}}(e,t.length-1),e}function Ss({duration:t=300,keyframes:e,times:s,ease:i="easeInOut"}){const n=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map($t):$t(i),r={done:!1,value:e[0]},o=function(t,e){return t.map(t=>t*e)}(s&&s.length===e.length?s:Ts(e),t),a=ws(o,e,{ease:Array.isArray(n)?n:(l=e,c=n,l.map(()=>c||zt).splice(0,l.length-1))});var l,c;return{calculatedDuration:t,next:e=>(r.value=a(e),r.done=e>=t,r)}}ys.applyToOptions=t=>{const e=function(t,e=100,s){const i=s({...t,keyframes:[0,e]}),n=Math.min(Ke(i),qe);return{type:"keyframes",ease:t=>i.next(n*t).value/e,duration:Pt(n)}}(t,100,ys);return t.ease=e.ease,t.duration=bt(e.duration),t.type="keyframes",t};const bs=t=>null!==t;function Ps(t,{repeat:e,repeatType:s="loop"},i,n=1){const r=t.filter(bs),o=n<0||e&&"loop"!==s&&e%2==1?0:r.length-1;return o&&void 0!==i?i:r[o]}const Es={decay:xs,inertia:xs,tween:Ss,keyframes:Ss,spring:ys};function Ms(t){"string"==typeof t.type&&(t.type=Es[t.type])}class Cs{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const As=t=>t/100;class ks extends Cs{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==Jt.now()&&this.tick(Jt.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ms(t);const{type:e=Ss,repeat:s=0,repeatDelay:i=0,repeatType:n,velocity:r=0}=t;let{keyframes:o}=t;const a=e||Ss;a!==Ss&&"number"!=typeof o[0]&&(this.mixKeyframes=wt(As,He(o[0],o[1])),o=[0,100]);const l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=Ke(l));const{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:s,totalDuration:i,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return s.next(0);const{delay:l=0,keyframes:c,repeat:d,repeatType:u,repeatDelay:h,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let y=this.currentTime,x=s;if(d){const t=Math.min(this.currentTime,i)/o;let e=Math.floor(t),s=t%1;!s&&t>=1&&(s=1),1===s&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===u?(s=1-s,h&&(s-=h/o)):"mirror"===u&&(x=r)),y=ht(0,1,s)*o}const w=v?{done:!1,value:c[0]}:x.next(y);n&&(w.value=n(w.value));let{done:T}=w;v||null===a||(T=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return S&&p!==xs&&(w.value=Ps(c,this.options,f,this.speed)),m&&m(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return Pt(this.calculatedDuration)}get time(){return Pt(this.currentTime)}set time(t){t=bt(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(Jt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=Pt(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Xe,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const s=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=s):null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime||(this.startTime=e??s),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(Jt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const Vs=t=>180*t/Math.PI,Ls=t=>{const e=Vs(Math.atan2(t[1],t[0]));return Is(e)},Ds={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ls,rotateZ:Ls,skewX:t=>Vs(Math.atan(t[1])),skewY:t=>Vs(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Is=t=>((t%=360)<0&&(t+=360),t),Os=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Rs=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Bs={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Os,scaleY:Rs,scale:t=>(Os(t)+Rs(t))/2,rotateX:t=>Is(Vs(Math.atan2(t[6],t[5]))),rotateY:t=>Is(Vs(Math.atan2(-t[2],t[0]))),rotateZ:Ls,rotate:Ls,skewX:t=>Vs(Math.atan(t[4])),skewY:t=>Vs(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Fs(t){return t.includes("scale")?1:0}function js(t,e){if(!t||"none"===t)return Fs(e);const s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,n;if(s)i=Bs,n=s;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Ds,n=e}if(!n)return Fs(e);const r=i[e],o=n[1].split(",").map(zs);return"function"==typeof r?r(o):o[r]}function zs(t){return parseFloat(t.trim())}const Ns=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Gs=(()=>new Set(Ns))(),$s=t=>t===ne||t===ye,_s=new Set(["x","y","z"]),Ws=Ns.filter(t=>!_s.has(t));const Us={width:({x:t},{paddingLeft:e="0",paddingRight:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),height:({y:t},{paddingTop:e="0",paddingBottom:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>js(e,"x"),y:(t,{transform:e})=>js(e,"y")};Us.translateX=Us.x,Us.translateY=Us.y;const Hs=new Set;let Xs=!1,Ys=!1,qs=!1;function Ks(){if(Ys){const t=Array.from(Hs).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),s=new Map;e.forEach(t=>{const e=function(t){const e=[];return Ws.forEach(s=>{const i=t.getValue(s);void 0!==i&&(e.push([s,i.get()]),i.set(s.startsWith("scale")?1:0))}),e}(t);e.length&&(s.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=s.get(t);e&&e.forEach(([e,s])=>{t.getValue(e)?.set(s)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Ys=!1,Xs=!1,Hs.forEach(t=>t.complete(qs)),Hs.clear()}function Zs(){Hs.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ys=!0)})}class Js{constructor(t,e,s,i,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=s,this.motionValue=i,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(Hs.add(this),Xs||(Xs=!0,Ht.read(Zs),Ht.resolveKeyframes(Ks))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:s,motionValue:i}=this;if(null===t[0]){const n=i?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(s&&e){const i=s.readValue(e,r);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=r),i&&void 0===n&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),Hs.delete(this)}cancel(){"scheduled"===this.state&&(Hs.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const Qs=vt(()=>void 0!==window.ScrollTimeline),ti={};function ei(t,e){const s=vt(t);return()=>ti[e]??s()}const si=ei(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),ii=([t,e,s,i])=>`cubic-bezier(${t}, ${e}, ${s}, ${i})`,ni={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ii([0,.65,.55,1]),circOut:ii([.55,0,1,.45]),backIn:ii([.31,.01,.66,-.59]),backOut:ii([.33,1.53,.69,.99])};function ri(t,e){return t?"function"==typeof t?si()?Ye(t,e):"ease-out":Nt(t)?ii(t):Array.isArray(t)?t.map(t=>ri(t,e)||ni.easeOut):ni[t]:void 0}function oi(t,e,s,{delay:i=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},c=void 0){const d={[e]:s};l&&(d.offset=l);const u=ri(a,n);Array.isArray(u)&&(d.easing=u);const h={delay:i,duration:n,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};c&&(h.pseudoElement=c);return t.animate(d,h)}function ai(t){return"function"==typeof t&&"applyToOptions"in t}class li extends Cs{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:s,keyframes:i,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(n),this.allowFlatten=r,this.options=t,t.type;const l=function({type:t,...e}){return ai(t)&&si()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=oi(e,s,i,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){const t=Ps(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,s){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,s):t.style[e]=s}(e,s,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return Pt(Number(t))}get time(){return Pt(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=bt(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Qs()?(this.animation.timeline=t,yt):e(this)}}const ci={anticipate:It,backInOut:Dt,circInOut:Bt};function di(t){"string"==typeof t.ease&&t.ease in ci&&(t.ease=ci[t.ease])}class ui extends li{constructor(t){di(t),Ms(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:s,onComplete:i,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new ks({...r,autoplay:!1}),a=bt(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const hi=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!De.test(t)&&"0"!==t||t.startsWith("url(")));function pi(t){return ft(t)&&"offsetHeight"in t}const mi=new Set(["opacity","clipPath","filter","transform"]),fi=vt(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class gi extends Cs{constructor({autoplay:t=!0,delay:e=0,type:s="keyframes",repeat:i=0,repeatDelay:n=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:c,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=Jt.now();const u={autoplay:t,delay:e,type:s,repeat:i,repeatDelay:n,repeatType:r,name:a,motionValue:l,element:c,...d},h=c?.KeyframeResolver||Js;this.keyframeResolver=new h(o,(t,e,s)=>this.onKeyframesResolved(t,e,u,!s),a,l,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,s,i){this.keyframeResolver=void 0;const{name:n,type:r,velocity:o,delay:a,isHandoff:l,onUpdate:c}=s;this.resolvedAt=Jt.now(),function(t,e,s,i){const n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],o=hi(n,e),a=hi(r,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let s=0;s<t.length;s++)if(t[s]!==e)return!0}(t)||("spring"===s||ai(s))&&i)}(t,n,r,o)||(!pt.instantAnimations&&a||c?.(Ps(t,s,e)),t[0]=t[t.length-1],s.duration=0,s.repeat=0);const d={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...s,keyframes:t},u=!l&&function(t){const{motionValue:e,name:s,repeatDelay:i,repeatType:n,damping:r,type:o}=t;if(!pi(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return fi()&&s&&mi.has(s)&&("transform"!==s||!l)&&!a&&!i&&"mirror"!==n&&0!==r&&"inertia"!==o}(d)?new ui({...d,element:d.motionValue.owner.current}):new ks(d);u.finished.then(()=>this.notifyFinished()).catch(yt),this.pendingTimeline&&(this.stopTimeline=u.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=u}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),qs=!0,Zs(),Ks(),qs=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const vi=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function yi(t,e,s=1){const[i,n]=function(t){const e=vi.exec(t);if(!e)return[,];const[,s,i,n]=e;return[`--${s??i}`,n]}(t);if(!i)return;const r=window.getComputedStyle(e).getPropertyValue(i);if(r){const t=r.trim();return mt(t)?parseFloat(t):t}return se(n)?yi(n,e,s+1):n}function xi(t,e){return t?.[e]??t?.default??t}const wi=new Set(["width","height","top","left","right","bottom",...Ns]),Ti=t=>e=>e.test(t),Si=[ne,ye,ve,ge,we,xe,{test:t=>"auto"===t,parse:t=>t}],bi=t=>Si.find(Ti(t));function Pi(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||gt(t))}const Ei=new Set(["brightness","contrast","saturate","opacity"]);function Mi(t){const[e,s]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=s.match(le)||[];if(!i)return t;const n=s.replace(i,"");let r=Ei.has(e)?1:0;return i!==s&&(r*=100),e+"("+r+n+")"}const Ci=/\b([a-z-]*)\(.*?\)/gu,Ai={...De,getAnimatableNone:t=>{const e=t.match(Ci);return e?e.map(Mi).join(" "):t}},ki={...ne,transform:Math.round},Vi={borderWidth:ye,borderTopWidth:ye,borderRightWidth:ye,borderBottomWidth:ye,borderLeftWidth:ye,borderRadius:ye,radius:ye,borderTopLeftRadius:ye,borderTopRightRadius:ye,borderBottomRightRadius:ye,borderBottomLeftRadius:ye,width:ye,maxWidth:ye,height:ye,maxHeight:ye,top:ye,right:ye,bottom:ye,left:ye,padding:ye,paddingTop:ye,paddingRight:ye,paddingBottom:ye,paddingLeft:ye,margin:ye,marginTop:ye,marginRight:ye,marginBottom:ye,marginLeft:ye,backgroundPositionX:ye,backgroundPositionY:ye,...{rotate:ge,rotateX:ge,rotateY:ge,rotateZ:ge,scale:oe,scaleX:oe,scaleY:oe,scaleZ:oe,skew:ge,skewX:ge,skewY:ge,distance:ye,translateX:ye,translateY:ye,translateZ:ye,x:ye,y:ye,z:ye,perspective:ye,transformPerspective:ye,opacity:re,originX:Te,originY:Te,originZ:ye},zIndex:ki,fillOpacity:re,strokeOpacity:re,numOctaves:ki},Li={...Vi,color:be,backgroundColor:be,outlineColor:be,fill:be,stroke:be,borderColor:be,borderTopColor:be,borderRightColor:be,borderBottomColor:be,borderLeftColor:be,filter:Ai,WebkitFilter:Ai},Di=t=>Li[t];function Ii(t,e){let s=Di(t);return s!==Ai&&(s=De),s.getAnimatableNone?s.getAnimatableNone(e):void 0}const Oi=new Set(["auto","none","0"]);class Ri extends Js{constructor(t,e,s,i,n){super(t,e,s,i,n,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:s}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let s=t[a];if("string"==typeof s&&(s=s.trim(),se(s))){const i=yi(s,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!wi.has(s)||2!==t.length)return;const[i,n]=t,r=bi(i),o=bi(n);if(r!==o)if($s(r)&&$s(o))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else Us[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,s=[];for(let i=0;i<t.length;i++)(null===t[i]||Pi(t[i]))&&s.push(i);s.length&&function(t,e,s){let i,n=0;for(;n<t.length&&!i;){const e=t[n];"string"==typeof e&&!Oi.has(e)&&Ae(e).values.length&&(i=t[n]),n++}if(i&&s)for(const r of e)t[r]=Ii(s,i)}(t,s,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:s}=this;if(!t||!t.current)return;"height"===s&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Us[s](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(s,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:s}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const n=s.length-1,r=s[n];s[n]=Us[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,s])=>{t.getValue(e).set(s)}),this.resolveNoneKeyframes()}}function Bi(t,e,s){if(t instanceof EventTarget)return[t];if("string"==typeof t){const e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}const Fi=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class ji{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const s=Jt.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const i of this.dependents)i.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=Jt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new St);const s=this.events[t].add(e);return"change"===t?()=>{s(),Ht.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,s){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-s}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=Jt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Et(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function zi(t,e){return new ji(t,e)}const{schedule:Ni}=Ut(queueMicrotask,!1),Gi={x:!1,y:!1};function $i(){return Gi.x||Gi.y}function _i(t,e){const s=Bi(t),i=new AbortController;return[s,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Wi(t){return!("touch"===t.pointerType||$i())}const Ui=(t,e)=>!!e&&(t===e||Ui(t,e.parentElement)),Hi=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Xi=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Yi=new WeakSet;function qi(t){return e=>{"Enter"===e.key&&t(e)}}function Ki(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Zi(t){return Hi(t)&&!$i()}function Ji(t,e,s={}){const[i,n,r]=_i(t,s),o=t=>{const i=t.currentTarget;if(!Zi(t))return;Yi.add(i);const r=e(i,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Yi.has(i)&&Yi.delete(i),Zi(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,i===window||i===document||s.useGlobalTarget||Ui(i,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return i.forEach(t=>{var e;(s.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),pi(t)&&(t.addEventListener("focus",t=>((t,e)=>{const s=t.currentTarget;if(!s)return;const i=qi(()=>{if(Yi.has(s))return;Ki(s,"down");const t=qi(()=>{Ki(s,"up")});s.addEventListener("keyup",t,e),s.addEventListener("blur",()=>Ki(s,"cancel"),e)});s.addEventListener("keydown",i,e),s.addEventListener("blur",()=>s.removeEventListener("keydown",i),e)})(t,n)),e=t,Xi.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}function Qi(t){return ft(t)&&"ownerSVGElement"in t}const tn=t=>Boolean(t&&t.getVelocity),en=[...Si,be,De],sn=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class nn extends t.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,s=pi(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=s-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function rn({children:e,isPresent:s,anchorX:i,root:n}){const o=t.useId(),a=t.useRef(null),l=t.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=t.useContext(sn);return t.useInsertionEffect(()=>{const{width:t,height:e,top:r,left:d,right:u}=l.current;if(s||!a.current||!t||!e)return;const h="left"===i?`left: ${d}`:`right: ${u}`;a.current.dataset.motionPopId=o;const p=document.createElement("style");c&&(p.nonce=c);const m=n??document.head;return m.appendChild(p),p.sheet&&p.sheet.insertRule(`\n          [data-motion-pop-id="${o}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${h}px !important;\n            top: ${r}px !important;\n          }\n        `),()=>{m.removeChild(p),m.contains(p)&&m.removeChild(p)}},[s]),r.jsx(nn,{isPresent:s,childRef:a,sizeRef:l,children:t.cloneElement(e,{ref:a})})}const on=({children:e,initial:s,isPresent:i,onExitComplete:n,custom:o,presenceAffectsLayout:a,mode:l,anchorX:c,root:d})=>{const u=ot(an),h=t.useId();let p=!0,m=t.useMemo(()=>(p=!1,{id:h,initial:s,isPresent:i,custom:o,onExitComplete:t=>{u.set(t,!0);for(const e of u.values())if(!e)return;n&&n()},register:t=>(u.set(t,!1),()=>u.delete(t))}),[i,u,n]);return a&&p&&(m={...m}),t.useMemo(()=>{u.forEach((t,e)=>u.set(e,!1))},[i]),t.useEffect(()=>{!i&&!u.size&&n&&n()},[i]),"popLayout"===l&&(e=r.jsx(rn,{isPresent:i,anchorX:c,root:d,children:e})),r.jsx(ct.Provider,{value:m,children:e})};function an(){return new Map}function ln(e=!0){const s=t.useContext(ct);if(null===s)return[!0,null];const{isPresent:i,onExitComplete:n,register:r}=s,o=t.useId();t.useEffect(()=>{if(e)return r(o)},[e]);const a=t.useCallback(()=>e&&n&&n(o),[o,n,e]);return!i&&n?[!1,a]:[!0]}const cn=t=>t.key||"";function dn(e){const s=[];return t.Children.forEach(e,e=>{t.isValidElement(e)&&s.push(e)}),s}const un=({children:e,custom:s,initial:i=!0,onExitComplete:n,presenceAffectsLayout:o=!0,mode:a="sync",propagate:l=!1,anchorX:c="left",root:d})=>{const[u,h]=ln(l),p=t.useMemo(()=>dn(e),[e]),m=l&&!u?[]:p.map(cn),f=t.useRef(!0),g=t.useRef(p),v=ot(()=>new Map),[y,x]=t.useState(p),[w,T]=t.useState(p);lt(()=>{f.current=!1,g.current=p;for(let t=0;t<w.length;t++){const e=cn(w[t]);m.includes(e)?v.delete(e):!0!==v.get(e)&&v.set(e,!1)}},[w,m.length,m.join("-")]);const S=[];if(p!==y){let t=[...p];for(let e=0;e<w.length;e++){const s=w[e],i=cn(s);m.includes(i)||(t.splice(e,0,s),S.push(s))}return"wait"===a&&S.length&&(t=S),T(dn(t)),x(p),null}const{forceRender:b}=t.useContext(rt);return r.jsx(r.Fragment,{children:w.map(t=>{const e=cn(t),y=!(l&&!u)&&(p===w||m.includes(e));return r.jsx(on,{isPresent:y,initial:!(f.current&&!i)&&void 0,custom:s,presenceAffectsLayout:o,mode:a,root:d,onExitComplete:y?void 0:()=>{if(!v.has(e))return;v.set(e,!0);let t=!0;v.forEach(e=>{e||(t=!1)}),t&&(b?.(),T(g.current),l&&h?.(),n&&n())},anchorX:c,children:t},e)})})},hn=t.createContext({strict:!1}),pn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},mn={};for(const ml in pn)mn[ml]={isEnabled:t=>pn[ml].some(e=>!!t[e])};const fn=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function gn(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||fn.has(t)}let vn=t=>!gn(t);try{"function"==typeof(yn=require("@emotion/is-prop-valid").default)&&(vn=t=>t.startsWith("on")?!gn(t):yn(t))}catch{}var yn;function xn(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy((...e)=>t(...e),{get:(s,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const wn=t.createContext({});function Tn(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function Sn(t){return"string"==typeof t||Array.isArray(t)}const bn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Pn=["initial",...bn];function En(t){return Tn(t.animate)||Pn.some(e=>Sn(t[e]))}function Mn(t){return Boolean(En(t)||t.variants)}function Cn(e){const{initial:s,animate:i}=function(t,e){if(En(t)){const{initial:e,animate:s}=t;return{initial:!1===e||Sn(e)?e:void 0,animate:Sn(s)?s:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(wn));return t.useMemo(()=>({initial:s,animate:i}),[An(s),An(i)])}function An(t){return Array.isArray(t)?t.join(" "):t}const kn=Symbol.for("motionComponentSymbol");function Vn(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function Ln(e,s,i){return t.useCallback(t=>{t&&e.onMount&&e.onMount(t),s&&(t?s.mount(t):s.unmount()),i&&("function"==typeof i?i(t):Vn(i)&&(i.current=t))},[s])}const Dn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),In="data-"+Dn("framerAppearId"),On=t.createContext({});function Rn(e,s,i,n,r){const{visualElement:o}=t.useContext(wn),a=t.useContext(hn),l=t.useContext(ct),c=t.useContext(sn).reducedMotion,d=t.useRef(null);n=n||a.renderer,!d.current&&n&&(d.current=n(e,{visualState:s,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:c}));const u=d.current,h=t.useContext(On);!u||u.projection||!r||"html"!==u.type&&"svg"!==u.type||function(t,e,s,i){const{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:d}=e;t.projection=new s(t.latestValues,e["data-framer-portal-id"]?void 0:Bn(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:Boolean(o)||a&&Vn(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:i,crossfade:d,layoutScroll:l,layoutRoot:c})}(d.current,i,r,h);const p=t.useRef(!1);t.useInsertionEffect(()=>{u&&p.current&&u.update(i,l)});const m=i[In],f=t.useRef(Boolean(m)&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return lt(()=>{u&&(p.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),Ni.render(u.render),f.current&&u.animationState&&u.animationState.animateChanges())}),t.useEffect(()=>{u&&(!f.current&&u.animationState&&u.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),u}function Bn(t){if(t)return!1!==t.options.allowProjection?t.projection:Bn(t.parent)}function Fn({preloadedFeatures:e,createVisualElement:s,useRender:i,useVisualState:n,Component:o}){function a(e,a){let l;const c={...t.useContext(sn),...e,layoutId:jn(e)},{isStatic:d}=c,u=Cn(e),h=n(e,d);if(!d&&at){t.useContext(hn).strict;const e=function(t){const{drag:e,layout:s}=mn;if(!e&&!s)return{};const i={...e,...s};return{MeasureLayout:e?.isEnabled(t)||s?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(c);l=e.MeasureLayout,u.visualElement=Rn(o,h,c,s,e.ProjectionNode)}return r.jsxs(wn.Provider,{value:u,children:[l&&u.visualElement?r.jsx(l,{visualElement:u.visualElement,...c}):null,i(o,e,Ln(h,u.visualElement,a),h,d,u.visualElement)]})}e&&function(t){for(const e in t)mn[e]={...mn[e],...t[e]}}(e),a.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const l=t.forwardRef(a);return l[kn]=o,l}function jn({layoutId:e}){const s=t.useContext(rt).id;return s&&void 0!==e?s+"-"+e:e}const zn={};function Nn(t,{layout:e,layoutId:s}){return Gs.has(t)||t.startsWith("origin")||(e||void 0!==s)&&(!!zn[t]||"opacity"===t)}const Gn={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},$n=Ns.length;function _n(t,e,s){const{style:i,vars:n,transformOrigin:r}=t;let o=!1,a=!1;for(const l in e){const t=e[l];if(Gs.has(l))o=!0;else if(te(l))n[l]=t;else{const e=Fi(t,Vi[l]);l.startsWith("origin")?(a=!0,r[l]=e):i[l]=e}}if(e.transform||(o||s?i.transform=function(t,e,s){let i="",n=!0;for(let r=0;r<$n;r++){const o=Ns[r],a=t[o];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a),!l||s){const t=Fi(a,Vi[o]);l||(n=!1,i+=`${Gn[o]||o}(${t}) `),s&&(e[o]=t)}}return i=i.trim(),s?i=s(e,n?"":i):n&&(i="none"),i}(e,t.transform,s):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:s=0}=r;i.transformOrigin=`${t} ${e} ${s}`}}const Wn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Un(t,e,s){for(const i in e)tn(e[i])||Nn(i,s)||(t[i]=e[i])}function Hn(e,s){const i={};return Un(i,e.style||{},e),Object.assign(i,function({transformTemplate:e},s){return t.useMemo(()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return _n(t,s,e),Object.assign({},t.vars,t.style)},[s])}(e,s)),i}function Xn(t,e){const s={},i=Hn(t,e);return t.drag&&!1!==t.dragListener&&(s.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=i,s}const Yn={offset:"stroke-dashoffset",array:"stroke-dasharray"},qn={offset:"strokeDashoffset",array:"strokeDasharray"};function Kn(t,{attrX:e,attrY:s,attrScale:i,pathLength:n,pathSpacing:r=1,pathOffset:o=0,...a},l,c,d){if(_n(t,a,c),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:u,style:h}=t;u.transform&&(h.transform=u.transform,delete u.transform),(h.transform||u.transformOrigin)&&(h.transformOrigin=u.transformOrigin??"50% 50%",delete u.transformOrigin),h.transform&&(h.transformBox=d?.transformBox??"fill-box",delete u.transformBox),void 0!==e&&(u.x=e),void 0!==s&&(u.y=s),void 0!==i&&(u.scale=i),void 0!==n&&function(t,e,s=1,i=0,n=!0){t.pathLength=1;const r=n?Yn:qn;t[r.offset]=ye.transform(-i);const o=ye.transform(e),a=ye.transform(s);t[r.array]=`${o} ${a}`}(u,n,r,o,!1)}const Zn=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Jn=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Qn(e,s,i,n){const r=t.useMemo(()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Kn(t,s,Jn(n),e.transformTemplate,e.style),{...t.attrs,style:{...t.style}}},[s]);if(e.style){const t={};Un(t,e.style,e),r.style={...t,...r.style}}return r}const tr=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function er(t){return"string"==typeof t&&!t.includes("-")&&!!(tr.indexOf(t)>-1||/[A-Z]/u.test(t))}function sr(e=!1){return(s,i,n,{latestValues:r},o)=>{const a=(er(s)?Qn:Xn)(i,r,o,s),l=function(t,e,s){const i={};for(const n in t)"values"===n&&"object"==typeof t.values||(vn(n)||!0===s&&gn(n)||!e&&!gn(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(i,"string"==typeof s,e),c=s!==t.Fragment?{...l,...a,ref:n}:{},{children:d}=i,u=t.useMemo(()=>tn(d)?d.get():d,[d]);return t.createElement(s,{...c,children:u})}}function ir(t){const e=[{},{}];return t?.values.forEach((t,s)=>{e[0][s]=t.get(),e[1][s]=t.getVelocity()}),e}function nr(t,e,s,i){if("function"==typeof e){const[n,r]=ir(i);e=e(void 0!==s?s:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[n,r]=ir(i);e=e(void 0!==s?s:t.custom,n,r)}return e}function rr(t){return tn(t)?t.get():t}const or=e=>(s,i)=>{const n=t.useContext(wn),r=t.useContext(ct),o=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},s,i,n){return{latestValues:ar(s,i,n,t),renderState:e()}}(e,s,n,r);return i?o():ot(o)};function ar(t,e,s,i){const n={},r=i(t,{});for(const h in r)n[h]=rr(r[h]);let{initial:o,animate:a}=t;const l=En(t),c=Mn(t);e&&c&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let d=!!s&&!1===s.initial;d=d||!1===o;const u=d?a:o;if(u&&"boolean"!=typeof u&&!Tn(u)){const e=Array.isArray(u)?u:[u];for(let s=0;s<e.length;s++){const i=nr(t,e[s]);if(i){const{transitionEnd:t,transition:e,...s}=i;for(const i in s){let t=s[i];if(Array.isArray(t)){t=t[d?t.length-1:0]}null!==t&&(n[i]=t)}for(const i in t)n[i]=t[i]}}}return n}function lr(t,e,s){const{style:i}=t,n={};for(const r in i)(tn(i[r])||e.style&&tn(e.style[r])||Nn(r,t)||void 0!==s?.getValue(r)?.liveStyle)&&(n[r]=i[r]);return n}const cr={useVisualState:or({scrapeMotionValuesFromProps:lr,createRenderState:Wn})};function dr(t,e,s){const i=lr(t,e,s);for(const n in t)if(tn(t[n])||tn(e[n])){i[-1!==Ns.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const ur={useVisualState:or({scrapeMotionValuesFromProps:dr,createRenderState:Zn})};function hr(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){return Fn({...er(s)?ur:cr,preloadedFeatures:t,useRender:sr(i),createVisualElement:e,Component:s})}}function pr(t,e,s){const i=t.getProps();return nr(i,e,void 0!==s?s:i.custom,t)}const mr=t=>Array.isArray(t);function fr(t,e,s){t.hasValue(e)?t.getValue(e).set(s):t.addValue(e,zi(s))}function gr(t){return mr(t)?t[t.length-1]||0:t}function vr(t,e){const s=t.getValue("willChange");if(i=s,Boolean(tn(i)&&i.add))return s.add(e);if(!s&&pt.WillChange){const s=new pt.WillChange("auto");t.addValue("willChange",s),s.add(e)}var i}function yr(t){return t.props[In]}const xr=t=>null!==t;const wr={type:"spring",stiffness:500,damping:25,restSpeed:10},Tr={type:"keyframes",duration:.8},Sr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},br=(t,{keyframes:e})=>e.length>2?Tr:Gs.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:wr:Sr;const Pr=(t,e,s,i={},n,r)=>o=>{const a=xi(i,t)||{},l=a.delay||i.delay||0;let{elapsed:c=0}=i;c-=bt(l);const d={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};(function({when:t,delay:e,delayChildren:s,staggerChildren:i,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:c,...d}){return!!Object.keys(d).length})(a)||Object.assign(d,br(t,d)),d.duration&&(d.duration=bt(d.duration)),d.repeatDelay&&(d.repeatDelay=bt(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let u=!1;if((!1===d.type||0===d.duration&&!d.repeatDelay)&&(d.duration=0,0===d.delay&&(u=!0)),(pt.instantAnimations||pt.skipAnimations)&&(u=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,u&&!r&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:s="loop"}){const i=t.filter(xr);return i[e&&"loop"!==s&&e%2==1?0:i.length-1]}(d.keyframes,a);if(void 0!==t)return void Ht.update(()=>{d.onUpdate(t),d.onComplete()})}return a.isSync?new ks(d):new gi(d)};function Er({protectedKeys:t,needsAnimating:e},s){const i=t.hasOwnProperty(s)&&!0!==e[s];return e[s]=!1,i}function Mr(t,e,{delay:s=0,transitionOverride:i,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;i&&(r=i);const l=[],c=n&&t.animationState&&t.animationState.getState()[n];for(const d in a){const e=t.getValue(d,t.latestValues[d]??null),i=a[d];if(void 0===i||c&&Er(c,d))continue;const n={delay:s,...xi(r||{},d)},o=e.get();if(void 0!==o&&!e.isAnimating&&!Array.isArray(i)&&i===o&&!n.velocity)continue;let u=!1;if(window.MotionHandoffAnimation){const e=yr(t);if(e){const t=window.MotionHandoffAnimation(e,d,Ht);null!==t&&(n.startTime=t,u=!0)}}vr(t,d),e.start(Pr(d,e,i,t.shouldReduceMotion&&wi.has(d)?{type:!1}:n,t,u));const h=e.animation;h&&l.push(h)}return o&&Promise.all(l).then(()=>{Ht.update(()=>{o&&function(t,e){const s=pr(t,e);let{transitionEnd:i={},transition:n={},...r}=s||{};r={...r,...i};for(const o in r)fr(t,o,gr(r[o]))}(t,o)})}),l}function Cr(t,e,s={}){const i=pr(t,e,"exit"===s.type?t.presenceContext?.custom:void 0);let{transition:n=t.getDefaultTransition()||{}}=i||{};s.transitionOverride&&(n=s.transitionOverride);const r=i?()=>Promise.all(Mr(t,i,s)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,s=0,i=0,n=1,r){const o=[],a=(t.variantChildren.size-1)*i,l=1===n?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(Ar).forEach((t,i)=>{t.notify("AnimationStart",e),o.push(Cr(t,e,{...r,delay:s+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+i,o,a,s)}:()=>Promise.resolve(),{when:a}=n;if(a){const[t,e]="beforeChildren"===a?[r,o]:[o,r];return t().then(()=>e())}return Promise.all([r(),o(s.delay)])}function Ar(t,e){return t.sortNodePosition(e)}function kr(t,e){if(!Array.isArray(e))return!1;const s=e.length;if(s!==t.length)return!1;for(let i=0;i<s;i++)if(e[i]!==t[i])return!1;return!0}const Vr=Pn.length;function Lr(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Lr(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let s=0;s<Vr;s++){const i=Pn[s],n=t.props[i];(Sn(n)||!1===n)&&(e[i]=n)}return e}const Dr=[...bn].reverse(),Ir=bn.length;function Or(t){return e=>Promise.all(e.map(({animation:e,options:s})=>function(t,e,s={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const n=e.map(e=>Cr(t,e,s));i=Promise.all(n)}else if("string"==typeof e)i=Cr(t,e,s);else{const n="function"==typeof e?pr(t,e,s.custom):e;i=Promise.all(Mr(t,n,s))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,e,s)))}function Rr(t){let e=Or(t),s=jr(),i=!0;const n=e=>(s,i)=>{const n=pr(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(n){const{transition:t,transitionEnd:e,...i}=n;s={...s,...i,...e}}return s};function r(r){const{props:o}=t,a=Lr(t.parent)||{},l=[],c=new Set;let d={},u=1/0;for(let e=0;e<Ir;e++){const h=Dr[e],p=s[h],m=void 0!==o[h]?o[h]:a[h],f=Sn(m),g=h===r?p.isActive:null;!1===g&&(u=e);let v=m===a[h]&&m!==o[h]&&f;if(v&&i&&t.manuallyAnimateOnMount&&(v=!1),p.protectedKeys={...d},!p.isActive&&null===g||!m&&!p.prevProp||Tn(m)||"boolean"==typeof m)continue;const y=Br(p.prevProp,m);let x=y||h===r&&p.isActive&&!v&&f||e>u&&f,w=!1;const T=Array.isArray(m)?m:[m];let S=T.reduce(n(h),{});!1===g&&(S={});const{prevResolvedValues:b={}}=p,P={...b,...S},E=e=>{x=!0,c.has(e)&&(w=!0,c.delete(e)),p.needsAnimating[e]=!0;const s=t.getValue(e);s&&(s.liveStyle=!1)};for(const t in P){const e=S[t],s=b[t];if(d.hasOwnProperty(t))continue;let i=!1;i=mr(e)&&mr(s)?!kr(e,s):e!==s,i?null!=e?E(t):c.add(t):void 0!==e&&c.has(t)?E(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(d={...d,...S}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(v&&y)||w)&&l.push(...T.map(t=>({animation:t,options:{type:h}})))}if(c.size){const e={};if("boolean"!=typeof o.initial){const s=pr(t,Array.isArray(o.initial)?o.initial[0]:o.initial);s&&s.transition&&(e.transition=s.transition)}c.forEach(s=>{const i=t.getBaseTarget(s),n=t.getValue(s);n&&(n.liveStyle=!0),e[s]=i??null}),l.push({animation:e})}let h=Boolean(l.length);return!i||!1!==o.initial&&o.initial!==o.animate||t.manuallyAnimateOnMount||(h=!1),i=!1,h?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,i){if(s[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),s[e].isActive=i;const n=r(e);for(const t in s)s[t].protectedKeys={};return n},setAnimateFunction:function(s){e=s(t)},getState:()=>s,reset:()=>{s=jr(),i=!0}}}function Br(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!kr(e,t)}function Fr(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function jr(){return{animate:Fr(!0),whileInView:Fr(),whileHover:Fr(),whileTap:Fr(),whileDrag:Fr(),whileFocus:Fr(),exit:Fr()}}class zr{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Nr=0;const Gr={animation:{Feature:class extends zr{constructor(t){super(t),t.animationState||(t.animationState=Rr(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Tn(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends zr{constructor(){super(...arguments),this.id=Nr++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===s)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function $r(t,e,s,i={passive:!0}){return t.addEventListener(e,s,i),()=>t.removeEventListener(e,s)}function _r(t){return{point:{x:t.pageX,y:t.pageY}}}function Wr(t,e,s,i){return $r(t,e,(t=>e=>Hi(e)&&t(e,_r(e)))(s),i)}function Ur({top:t,left:e,right:s,bottom:i}){return{x:{min:e,max:s},y:{min:t,max:i}}}function Hr(t){return t.max-t.min}function Xr(t,e,s,i=.5){t.origin=i,t.originPoint=Re(e.min,e.max,t.origin),t.scale=Hr(s)/Hr(e),t.translate=Re(s.min,s.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Yr(t,e,s,i){Xr(t.x,e.x,s.x,i?i.originX:void 0),Xr(t.y,e.y,s.y,i?i.originY:void 0)}function qr(t,e,s){t.min=s.min+e.min,t.max=t.min+Hr(e)}function Kr(t,e,s){t.min=e.min-s.min,t.max=t.min+Hr(e)}function Zr(t,e,s){Kr(t.x,e.x,s.x),Kr(t.y,e.y,s.y)}const Jr=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Qr(t){return[t("x"),t("y")]}function to(t){return void 0===t||1===t}function eo({scale:t,scaleX:e,scaleY:s}){return!to(t)||!to(e)||!to(s)}function so(t){return eo(t)||io(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function io(t){return no(t.x)||no(t.y)}function no(t){return t&&"0%"!==t}function ro(t,e,s){return s+e*(t-s)}function oo(t,e,s,i,n){return void 0!==n&&(t=ro(t,n,i)),ro(t,s,i)+e}function ao(t,e=0,s=1,i,n){t.min=oo(t.min,e,s,i,n),t.max=oo(t.max,e,s,i,n)}function lo(t,{x:e,y:s}){ao(t.x,e.translate,e.scale,e.originPoint),ao(t.y,s.translate,s.scale,s.originPoint)}const co=.999999999999,uo=1.0000000000001;function ho(t,e){t.min=t.min+e,t.max=t.max+e}function po(t,e,s,i,n=.5){ao(t,e,s,Re(t.min,t.max,n),i)}function mo(t,e){po(t.x,e.x,e.scaleX,e.scale,e.originX),po(t.y,e.y,e.scaleY,e.scale,e.originY)}function fo(t,e){return Ur(function(t,e){if(!e)return t;const s=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:s.y,left:s.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const go=({current:t})=>t?t.ownerDocument.defaultView:null,vo=(t,e)=>Math.abs(t-e);class yo{constructor(t,e,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=To(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,s=function(t,e){const s=vo(t.x,e.x),i=vo(t.y,e.y);return Math.sqrt(s**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!s)return;const{point:i}=t,{timestamp:n}=Yt;this.history.push({...i,timestamp:n});const{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=xo(e,this.transformPagePoint),Ht.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:s,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const r=To("pointercancel"===t.type?this.lastMoveEventInfo:xo(e,this.transformPagePoint),this.history);this.startEvent&&s&&s(t,r),i&&i(t,r)},!Hi(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=s,this.contextWindow=i||window;const r=xo(_r(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=Yt;this.history=[{...o,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,To(r,this.history)),this.removeListeners=wt(Wr(this.contextWindow,"pointermove",this.handlePointerMove),Wr(this.contextWindow,"pointerup",this.handlePointerUp),Wr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Xt(this.updatePoint)}}function xo(t,e){return e?{point:e(t.point)}:t}function wo(t,e){return{x:t.x-e.x,y:t.y-e.y}}function To({point:t},e){return{point:t,delta:wo(t,bo(e)),offset:wo(t,So(e)),velocity:Po(e,.1)}}function So(t){return t[0]}function bo(t){return t[t.length-1]}function Po(t,e){if(t.length<2)return{x:0,y:0};let s=t.length-1,i=null;const n=bo(t);for(;s>=0&&(i=t[s],!(n.timestamp-i.timestamp>bt(e)));)s--;if(!i)return{x:0,y:0};const r=Pt(n.timestamp-i.timestamp);if(0===r)return{x:0,y:0};const o={x:(n.x-i.x)/r,y:(n.y-i.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Eo(t,e,s){return{min:void 0!==e?t.min+e:void 0,max:void 0!==s?t.max+s-(t.max-t.min):void 0}}function Mo(t,e){let s=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([s,i]=[i,s]),{min:s,max:i}}const Co=.35;function Ao(t,e,s){return{min:ko(t,e),max:ko(t,s)}}function ko(t,e){return"number"==typeof t?t:t[e]||0}const Vo=new WeakMap;class Lo{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new yo(t,{onSessionStart:t=>{const{dragSnapToOrigin:s}=this.getProps();s?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(_r(t).point)},onStart:(t,e)=>{const{drag:s,dragPropagation:i,onDragStart:n}=this.getProps();if(s&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(r=s)||"y"===r?Gi[r]?null:(Gi[r]=!0,()=>{Gi[r]=!1}):Gi.x||Gi.y?null:(Gi.x=Gi.y=!0,()=>{Gi.x=Gi.y=!1}),!this.openDragLock))return;var r;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Qr(t=>{let e=this.getAxisMotionValue(t).get()||0;if(ve.test(e)){const{projection:s}=this.visualElement;if(s&&s.layout){const i=s.layout.layoutBox[t];if(i){e=Hr(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),n&&Ht.postRender(()=>n(t,e)),vr(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:s,dragDirectionLock:i,onDirectionLock:n,onDrag:r}=this.getProps();if(!s&&!this.openDragLock)return;const{offset:o}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let s=null;Math.abs(t.y)>e?s="y":Math.abs(t.x)>e&&(s="x");return s}(o),void(null!==this.currentDirection&&n&&n(this.currentDirection));this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Qr(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:go(this.visualElement)})}stop(t,e){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:n}=this.getProps();n&&Ht.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,s){const{drag:i}=this.getProps();if(!s||!Do(t,i,this.currentDirection))return;const n=this.getAxisMotionValue(t);let r=this.originPoint[t]+s[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:s},i){return void 0!==e&&t<e?t=i?Re(e,t,i.min):Math.max(t,e):void 0!==s&&t>s&&(t=i?Re(s,t,i.max):Math.min(t,s)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&Vn(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!s)&&function(t,{top:e,left:s,bottom:i,right:n}){return{x:Eo(t.x,s,n),y:Eo(t.y,e,i)}}(s.layoutBox,t),this.elastic=function(t=Co){return!1===t?t=0:!0===t&&(t=Co),{x:Ao(t,"left","right"),y:Ao(t,"top","bottom")}}(e),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&Qr(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const s={};return void 0!==e.min&&(s.min=e.min-t.min),void 0!==e.max&&(s.max=e.max-t.min),s}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Vn(t))return!1;const s=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const n=function(t,e,s){const i=fo(t,s),{scroll:n}=e;return n&&(ho(i.x,n.offset.x),ho(i.y,n.offset.y)),i}(s,i.root,this.visualElement.getTransformPagePoint());let r=function(t,e){return{x:Mo(t.x,e.x),y:Mo(t.y,e.y)}}(i.layout.layoutBox,n);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(r));this.hasMutatedConstraints=!!t,t&&(r=Ur(t))}return r}startAnimation(t){const{drag:e,dragMomentum:s,dragElastic:i,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{},l=Qr(o=>{if(!Do(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});const c=i?200:1e6,d=i?40:1e7,u={type:"inertia",velocity:s?t[o]:0,bounceStiffness:c,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)});return Promise.all(l).then(o)}startAxisValueAnimation(t,e){const s=this.getAxisMotionValue(t);return vr(this.visualElement,t),s.start(Pr(t,s,0,e,this.visualElement,!1))}stopAnimation(){Qr(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Qr(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,s=this.visualElement.getProps(),i=s[e];return i||this.visualElement.getValue(t,(s.initial?s.initial[t]:void 0)||0)}snapToCursor(t){Qr(e=>{const{drag:s}=this.getProps();if(!Do(e,s,this.currentDirection))return;const{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){const{min:s,max:r}=i.layout.layoutBox[e];n.set(t[e]-Re(s,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:s}=this.visualElement;if(!Vn(e)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Qr(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const s=e.get();i[t]=function(t,e){let s=.5;const i=Hr(t),n=Hr(e);return n>i?s=Tt(e.min,e.max-i,t.min):i>n&&(s=Tt(t.min,t.max-n,e.min)),ht(0,1,s)}({min:s,max:s},this.constraints[t])}});const{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),Qr(e=>{if(!Do(e,t,null))return;const s=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];s.set(Re(n,r,i[e]))})}addListeners(){if(!this.visualElement.current)return;Vo.set(this.visualElement,this);const t=Wr(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:s=!0}=this.getProps();e&&s&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();Vn(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",e);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),Ht.read(e);const n=$r(window,"resize",()=>this.scalePositionWithinConstraints()),r=s.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Qr(e=>{const s=this.getAxisMotionValue(e);s&&(this.originPoint[e]+=t[e].translate,s.set(s.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),r&&r()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:r=Co,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:s,dragPropagation:i,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function Do(t,e,s){return!(!0!==e&&e!==t||null!==s&&s!==t)}const Io=t=>(e,s)=>{t&&Ht.postRender(()=>t(e,s))};const Oo={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ro(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Bo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!ye.test(t))return t;t=parseFloat(t)}return`${Ro(t,e.target.x)}% ${Ro(t,e.target.y)}%`}},Fo={correct:(t,{treeScale:e,projectionDelta:s})=>{const i=t,n=De.parse(t);if(n.length>5)return i;const r=De.createTransformer(t),o="number"!=typeof n[0]?1:0,a=s.x.scale*e.x,l=s.y.scale*e.y;n[0+o]/=a,n[1+o]/=l;const c=Re(a,l,.5);return"number"==typeof n[2+o]&&(n[2+o]/=c),"number"==typeof n[3+o]&&(n[3+o]/=c),r(n)}};class jo extends t.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:s,layoutId:i}=this.props,{projection:n}=t;!function(t){for(const e in t)zn[e]=t[e],te(e)&&(zn[e].isCSSVariable=!0)}(No),n&&(e.group&&e.group.add(n),s&&s.register&&i&&s.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),Oo.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:s,drag:i,isPresent:n}=this.props,{projection:r}=s;return r?(r.isPresent=n,i||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||Ht.postRender(()=>{const t=r.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Ni.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:s}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function zo(e){const[s,i]=ln(),n=t.useContext(rt);return r.jsx(jo,{...e,layoutGroup:n,switchLayoutGroup:t.useContext(On),isPresent:s,safeToRemove:i})}const No={borderRadius:{...Bo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Bo,borderTopRightRadius:Bo,borderBottomLeftRadius:Bo,borderBottomRightRadius:Bo,boxShadow:Fo};const Go=(t,e)=>t.depth-e.depth;class $o{constructor(){this.children=[],this.isDirty=!1}add(t){dt(this.children,t),this.isDirty=!0}remove(t){ut(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Go),this.isDirty=!1,this.children.forEach(t)}}const _o=["TopLeft","TopRight","BottomLeft","BottomRight"],Wo=_o.length,Uo=t=>"string"==typeof t?parseFloat(t):t,Ho=t=>"number"==typeof t||ye.test(t);function Xo(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Yo=Ko(0,.5,Rt),qo=Ko(.5,.95,yt);function Ko(t,e,s){return i=>i<t?0:i>e?1:s(Tt(t,e,i))}function Zo(t,e){t.min=e.min,t.max=e.max}function Jo(t,e){Zo(t.x,e.x),Zo(t.y,e.y)}function Qo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ta(t,e,s,i,n){return t=ro(t-=e,1/s,i),void 0!==n&&(t=ro(t,1/n,i)),t}function ea(t,e,[s,i,n],r,o){!function(t,e=0,s=1,i=.5,n,r=t,o=t){ve.test(e)&&(e=parseFloat(e),e=Re(o.min,o.max,e/100)-o.min);if("number"!=typeof e)return;let a=Re(r.min,r.max,i);t===r&&(a-=e),t.min=ta(t.min,e,s,a,n),t.max=ta(t.max,e,s,a,n)}(t,e[s],e[i],e[n],e.scale,r,o)}const sa=["x","scaleX","originX"],ia=["y","scaleY","originY"];function na(t,e,s,i){ea(t.x,e,sa,s?s.x:void 0,i?i.x:void 0),ea(t.y,e,ia,s?s.y:void 0,i?i.y:void 0)}function ra(t){return 0===t.translate&&1===t.scale}function oa(t){return ra(t.x)&&ra(t.y)}function aa(t,e){return t.min===e.min&&t.max===e.max}function la(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ca(t,e){return la(t.x,e.x)&&la(t.y,e.y)}function da(t){return Hr(t.x)/Hr(t.y)}function ua(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ha{constructor(){this.members=[]}add(t){dt(this.members,t),t.scheduleRender()}remove(t){if(ut(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let s;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){s=t;break}}return!!s&&(this.promote(s),!0)}promote(t,e){const s=this.lead;if(t!==s&&(this.prevLead=s,this.lead=t,t.show(),s)){s.instance&&s.scheduleRender(),t.scheduleRender(),t.resumeFrom=s,e&&(t.resumeFrom.preserveOpacity=!0),s.snapshot&&(t.snapshot=s.snapshot,t.snapshot.latestValues=s.animationValues||s.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&s.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:s}=t;e.onExitComplete&&e.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const pa=["","X","Y","Z"],ma={visibility:"hidden"};let fa=0;function ga(t,e,s,i){const{latestValues:n}=e;n[t]&&(s[t]=n[t],e.setStaticValue(t,0),i&&(i[t]=0))}function va(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const s=yr(e);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",Ht,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&va(i)}function ya({attachResizeListener:t,defaultParent:e,measureScroll:s,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},s=e?.()){this.id=fa++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Ta),this.nodes.forEach(Aa),this.nodes.forEach(ka),this.nodes.forEach(Sa)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=s?s.root||s:this,this.path=s?[...s.path,s]:[],this.parent=s,this.depth=s?s.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new $o)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new St),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const s=this.eventHandlers.get(t);s&&s.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var s;this.isSVG=Qi(e)&&!(Qi(s=e)&&"svg"===s.tagName),this.instance=e;const{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let s;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,s&&s(),s=function(t,e){const s=Jt.now(),i=({timestamp:n})=>{const r=n-s;r>=e&&(Xt(i),t(r-e))};return Ht.setup(i,!0),()=>Xt(i)}(i,250),Oo.hasAnimatedSinceResize&&(Oo.hasAnimatedSinceResize=!1,this.nodes.forEach(Ca))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:s,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const n=this.options.transition||r.getDefaultTransition()||Ra,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!ca(this.targetLayout,i),c=!e&&s;if(this.options.layoutRoot||this.resumeFrom||c||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...xi(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,c)}else e||Ca(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Xt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Va),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&va(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let n=0;n<this.path.length;n++){const t=this.path[n];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:s}=this.options;if(void 0===e&&!s)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Pa);if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(Ea);this.isUpdating||this.nodes.forEach(Ea),this.animationCommitId=this.animationId,this.isUpdating=!1,this.nodes.forEach(Ma),this.nodes.forEach(xa),this.nodes.forEach(wa),this.clearAllSnapshots();const t=Jt.now();Yt.delta=ht(0,1e3/60,t-Yt.timestamp),Yt.timestamp=t,Yt.isProcessing=!0,qt.update.process(Yt),qt.preRender.process(Yt),qt.render.process(Yt),Yt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ni.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ba),this.sharedNodes.forEach(La)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Ht.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Ht.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Hr(this.snapshot.measuredBox.x)||Hr(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let s=0;s<this.path.length;s++){this.path[s].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!oa(this.projectionDelta),s=this.getTransformTemplate(),i=s?s(this.latestValues,""):void 0,r=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||so(this.latestValues)||r)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let s=this.removeElementScroll(e);var i;return t&&(s=this.removeTransform(s)),ja((i=s).x),ja(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Na))){const{scroll:t}=this.root;t&&(ho(e.x,t.offset.x),ho(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(Jo(e,t),this.scroll?.wasRoot)return e;for(let s=0;s<this.path.length;s++){const i=this.path[s],{scroll:n,options:r}=i;i!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&Jo(e,t),ho(e.x,n.offset.x),ho(e.y,n.offset.y))}return e}applyTransform(t,e=!1){const s={x:{min:0,max:0},y:{min:0,max:0}};Jo(s,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&mo(s,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),so(t.latestValues)&&mo(s,t.latestValues)}return so(this.latestValues)&&mo(s,this.latestValues),s}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Jo(e,t);for(let s=0;s<this.path.length;s++){const t=this.path[s];if(!t.instance)continue;if(!so(t.latestValues))continue;eo(t.latestValues)&&t.updateSnapshot();const i=Jr();Jo(i,t.measurePageBox()),na(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return so(this.latestValues)&&na(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Yt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const s=Boolean(this.resumingFrom)||this!==e;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:n}=this.options;if(this.layout&&(i||n)){if(this.resolvedRelativeTargetAt=Yt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Zr(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Jo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var r,o,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,o=this.relativeTarget,a=this.relativeParent.target,qr(r.x,o.x,a.x),qr(r.y,o.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Jo(this.target,this.layout.layoutBox),lo(this.target,this.targetDelta)):Jo(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Zr(this.relativeTargetOrigin,this.target,t.target),Jo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!eo(this.parent.latestValues)&&!io(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let s=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(s=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===Yt.timestamp&&(s=!1),s)return;const{layout:i,layoutId:n}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!n)return;Jo(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,o=this.treeScale.y;!function(t,e,s,i=!1){const n=s.length;if(!n)return;let r,o;e.x=e.y=1;for(let a=0;a<n;a++){r=s[a],o=r.projectionDelta;const{visualElement:n}=r.options;n&&n.props.style&&"contents"===n.props.style.display||(i&&r.options.layoutScroll&&r.scroll&&r!==r.root&&mo(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,lo(t,o)),i&&so(r.latestValues)&&mo(t,r.latestValues))}e.x<uo&&e.x>co&&(e.x=1),e.y<uo&&e.y>co&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(Qo(this.prevProjectionDelta.x,this.projectionDelta.x),Qo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Yr(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===r&&this.treeScale.y===o&&ua(this.projectionDelta.x,this.prevProjectionDelta.x)&&ua(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const s=this.snapshot,i=s?s.latestValues:{},n={...this.latestValues},r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const o={x:{min:0,max:0},y:{min:0,max:0}},a=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),c=!l||l.members.length<=1,d=Boolean(a&&!c&&!0===this.options.crossfade&&!this.path.some(Oa));let u;this.animationProgress=0,this.mixTargetDelta=e=>{const s=e/1e3;var l,h,p,m,f,g;Da(r.x,t.x,s),Da(r.y,t.y,s),this.setTargetDelta(r),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Zr(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=s,Ia(p.x,m.x,f.x,g),Ia(p.y,m.y,f.y,g),u&&(l=this.relativeTarget,h=u,aa(l.x,h.x)&&aa(l.y,h.y))&&(this.isProjectionDirty=!1),u||(u={x:{min:0,max:0},y:{min:0,max:0}}),Jo(u,this.relativeTarget)),a&&(this.animationValues=n,function(t,e,s,i,n,r){n?(t.opacity=Re(0,s.opacity??1,Yo(i)),t.opacityExit=Re(e.opacity??1,0,qo(i))):r&&(t.opacity=Re(e.opacity??1,s.opacity??1,i));for(let o=0;o<Wo;o++){const n=`border${_o[o]}Radius`;let r=Xo(e,n),a=Xo(s,n);void 0===r&&void 0===a||(r||(r=0),a||(a=0),0===r||0===a||Ho(r)===Ho(a)?(t[n]=Math.max(Re(Uo(r),Uo(a),i),0),(ve.test(a)||ve.test(r))&&(t[n]+="%")):t[n]=a)}(e.rotate||s.rotate)&&(t.rotate=Re(e.rotate||0,s.rotate||0,i))}(n,i,this.latestValues,s,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Xt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Ht.update(()=>{Oo.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=zi(0)),this.currentAnimation=function(t,e,s){const i=tn(t)?t:zi(t);return i.start(Pr("",i,e,s)),i.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:s,layout:i,latestValues:n}=t;if(e&&s&&i){if(this!==t&&this.layout&&i&&za(this.options.animationType,this.layout.layoutBox,i.layoutBox)){s=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Hr(this.layout.layoutBox.x);s.x.min=t.target.x.min,s.x.max=s.x.min+e;const i=Hr(this.layout.layoutBox.y);s.y.min=t.target.y.min,s.y.max=s.y.min+i}Jo(e,s),mo(e,n),Yr(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new ha);this.sharedNodes.get(t).add(e);const s=e.options.initialPromotionConfig;e.promote({transition:s?s.transition:void 0,preserveFollowOpacity:s&&s.shouldPreserveFollowOpacity?s.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:s}={}){const i=this.getStack();i&&i.promote(this,s),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:s}=t;if((s.z||s.rotate||s.rotateX||s.rotateY||s.rotateZ||s.skewX||s.skewY)&&(e=!0),!e)return;const i={};s.z&&ga("z",t,i,this.animationValues);for(let n=0;n<pa.length;n++)ga(`rotate${pa[n]}`,t,i,this.animationValues),ga(`skew${pa[n]}`,t,i,this.animationValues);t.render();for(const n in i)t.setStaticValue(n,i[n]),this.animationValues&&(this.animationValues[n]=i[n]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ma;const e={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=rr(t?.pointerEvents)||"",e.transform=s?s(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rr(t?.pointerEvents)||""),this.hasProjected&&!so(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const n=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,s){let i="";const n=t.x.translate/e.x,r=t.y.translate/e.y,o=s?.z||0;if((n||r||o)&&(i=`translate3d(${n}px, ${r}px, ${o}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),s){const{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=s;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),n&&(i+=`rotateX(${n}deg) `),r&&(i+=`rotateY(${r}deg) `),o&&(i+=`skewX(${o}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),s&&(e.transform=s(n,e.transform));const{x:r,y:o}=this.projectionDelta;e.transformOrigin=`${100*r.origin}% ${100*o.origin}% 0`,i.animationValues?e.opacity=i===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=i===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0;for(const a in zn){if(void 0===n[a])continue;const{correct:t,applyTo:s,isCSSVariable:r}=zn[a],o="none"===e.transform?n[a]:t(n[a],i);if(s){const t=s.length;for(let i=0;i<t;i++)e[s[i]]=o}else r?this.options.visualElement.renderState.vars[a]=o:e[a]=o}return this.options.layoutId&&(e.pointerEvents=i===this?rr(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(Pa),this.root.sharedNodes.clear()}}}function xa(t){t.updateLayout()}function wa(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?Qr(t=>{const i=r?e.measuredBox[t]:e.layoutBox[t],n=Hr(i);i.min=s[t].min,i.max=i.min+n}):za(n,e.layoutBox,s)&&Qr(i=>{const n=r?e.measuredBox[i]:e.layoutBox[i],o=Hr(s[i]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+o)});const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Yr(o,s,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};r?Yr(a,t.applyTransform(i,!0),e.measuredBox):Yr(a,s,e.layoutBox);const l=!oa(o);let c=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:n,layout:r}=i;if(n&&r){const o={x:{min:0,max:0},y:{min:0,max:0}};Zr(o,e.layoutBox,n.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Zr(a,s,r.layoutBox),ca(o,a)||(c=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function Ta(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Sa(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function ba(t){t.clearSnapshot()}function Pa(t){t.clearMeasurements()}function Ea(t){t.isLayoutDirty=!1}function Ma(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Ca(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Aa(t){t.resolveTargetDelta()}function ka(t){t.calcProjection()}function Va(t){t.resetSkewAndRotation()}function La(t){t.removeLeadSnapshot()}function Da(t,e,s){t.translate=Re(e.translate,0,s),t.scale=Re(e.scale,1,s),t.origin=e.origin,t.originPoint=e.originPoint}function Ia(t,e,s,i){t.min=Re(e.min,s.min,i),t.max=Re(e.max,s.max,i)}function Oa(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Ra={duration:.45,ease:[.4,0,.1,1]},Ba=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Fa=Ba("applewebkit/")&&!Ba("chrome/")?Math.round:yt;function ja(t){t.min=Fa(t.min),t.max=Fa(t.max)}function za(t,e,s){return"position"===t||"preserve-aspect"===t&&(i=da(e),n=da(s),r=.2,!(Math.abs(i-n)<=r));var i,n,r}function Na(t){return t!==t.root&&t.scroll?.wasRoot}const Ga=ya({attachResizeListener:(t,e)=>$r(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),$a={current:void 0},_a=ya({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!$a.current){const t=new Ga({});t.mount(window),t.setOptions({layoutScroll:!0}),$a.current=t}return $a.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Wa={pan:{Feature:class extends zr{constructor(){super(...arguments),this.removePointerDownListener=yt}onPointerDown(t){this.session=new yo(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:go(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:Io(t),onStart:Io(e),onMove:s,onEnd:(t,e)=>{delete this.session,i&&Ht.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=Wr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends zr{constructor(t){super(t),this.removeGroupControls=yt,this.removeListeners=yt,this.controls=new Lo(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||yt}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:_a,MeasureLayout:zo}};function Ua(t,e,s){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===s);const n=i["onHover"+s];n&&Ht.postRender(()=>n(e,_r(e)))}function Ha(t,e,s){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===s);const n=i["onTap"+("End"===s?"":s)];n&&Ht.postRender(()=>n(e,_r(e)))}const Xa=new WeakMap,Ya=new WeakMap,qa=t=>{const e=Xa.get(t.target);e&&e(t)},Ka=t=>{t.forEach(qa)};function Za(t,e,s){const i=function({root:t,...e}){const s=t||document;Ya.has(s)||Ya.set(s,{});const i=Ya.get(s),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(Ka,{root:t,...e})),i[n]}(e);return Xa.set(t,s),i.observe(t),()=>{Xa.delete(t),i.unobserve(t)}}const Ja={some:0,all:1};const Qa={inView:{Feature:class extends zr{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:s,amount:i="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:s,threshold:"number"==typeof i?i:Ja[i]};return Za(this.node.current,r,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,n&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:s,onViewportLeave:i}=this.node.getProps(),r=e?s:i;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return s=>t[s]!==e[s]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends zr{mount(){const{current:t}=this.node;t&&(this.unmount=Ji(t,(t,e)=>(Ha(this.node,e,"Start"),(t,{success:e})=>Ha(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends zr{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=wt($r(this.node.current,"focus",()=>this.onFocus()),$r(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends zr{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,s={}){const[i,n,r]=_i(t,s),o=t=>{if(!Wi(t))return;const{target:s}=t,i=e(s,t);if("function"!=typeof i||!s)return;const r=t=>{Wi(t)&&(i(t),s.removeEventListener("pointerleave",r))};s.addEventListener("pointerleave",r,n)};return i.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,(t,e)=>(Ua(this.node,e,"Start"),t=>Ua(this.node,t,"End"))))}unmount(){}}}},tl={layout:{ProjectionNode:_a,MeasureLayout:zo}},el={current:null},sl={current:!1};const il=new WeakMap;const nl=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rl{scrapeMotionValuesFromProps(t,e,s){return{}}constructor({parent:t,props:e,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Js,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=Jt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,Ht.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=s,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.blockInitialAnimation=Boolean(n),this.isControllingVariants=En(e),this.isVariantNode=Mn(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(const u in d){const t=d[u];void 0!==a[u]&&tn(t)&&t.set(a[u],!1)}}mount(t){this.current=t,il.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sl.current||function(){if(sl.current=!0,at)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>el.current=t.matches;t.addListener(e),e()}else el.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||el.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Xt(this.notifyUpdate),Xt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const s=Gs.has(t);s&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&Ht.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),n(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in mn){const e=mn[t];if(!e)continue;const{isEnabled:s,Feature:i}=e;if(!this.features[t]&&i&&s(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let s=0;s<nl.length;s++){const e=nl[s];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,s){for(const i in e){const n=e[i],r=s[i];if(tn(n))t.addValue(i,n);else if(tn(r))t.addValue(i,zi(n,{owner:t}));else if(r!==n)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{const e=t.getStaticValue(i);t.addValue(i,zi(void 0!==e?e:n,{owner:t}))}}for(const i in s)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const s=this.values.get(t);e!==s&&(s&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let s=this.values.get(t);return void 0===s&&void 0!==e&&(s=zi(null===e?void 0:e,{owner:this}),this.addValue(t,s)),s}readValue(t,e){let s=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=s&&("string"==typeof s&&(mt(s)||gt(s))?s=parseFloat(s):(i=s,!en.find(Ti(i))&&De.test(e)&&(s=Ii(t,e))),this.setBaseTarget(t,tn(s)?s.get():s)),tn(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let s;if("string"==typeof e||"object"==typeof e){const i=nr(this.props,e,this.presenceContext?.custom);i&&(s=i[t])}if(e&&void 0!==s)return s;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||tn(i)?void 0!==this.initialValues[t]&&void 0===s?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new St),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class ol extends rl{constructor(){super(...arguments),this.KeyframeResolver=Ri}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:s}){delete e[t],delete s[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;tn(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function al(t,{style:e,vars:s},i,n){Object.assign(t.style,e,n&&n.getProjectionStyles(i));for(const r in s)t.style.setProperty(r,s[r])}class ll extends ol{constructor(){super(...arguments),this.type="html",this.renderInstance=al}readValueFromInstance(t,e){if(Gs.has(e))return this.projection?.isProjecting?Fs(e):((t,e)=>{const{transform:s="none"}=getComputedStyle(t);return js(s,e)})(t,e);{const i=(s=t,window.getComputedStyle(s)),n=(te(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}var s}measureInstanceViewportBox(t,{transformPagePoint:e}){return fo(t,e)}build(t,e,s){_n(t,e,s.transformTemplate)}scrapeMotionValuesFromProps(t,e,s){return lr(t,e,s)}}const cl=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class dl extends ol{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Jr}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Gs.has(e)){const t=Di(e);return t&&t.default||0}return e=cl.has(e)?e:Dn(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,s){return dr(t,e,s)}build(t,e,s){Kn(t,e,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(t,e,s,i){!function(t,e,s,i){al(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(cl.has(n)?n:Dn(n),e.attrs[n])}(t,e,0,i)}mount(t){this.isSVGTag=Jn(t.tagName),super.mount(t)}}const ul=xn(hr({...Gr,...Qa,...Wa,...tl},(e,s)=>er(e)?new dl(s):new ll(s,{allowProjection:e!==t.Fragment}))),hl={some:0,all:1};function pl(e,{root:s,margin:i,amount:n,once:r=!1,initial:o=!1}={}){const[a,l]=t.useState(o);return t.useEffect(()=>{if(!e.current||r&&a)return;const t={root:s&&s.current||void 0,margin:i,amount:n};return function(t,e,{root:s,margin:i,amount:n="some"}={}){const r=Bi(t),o=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const s=o.get(t.target);if(t.isIntersecting!==Boolean(s))if(t.isIntersecting){const s=e(t.target,t);"function"==typeof s?o.set(t.target,s):a.unobserve(t.target)}else"function"==typeof s&&(s(t),o.delete(t.target))})},{root:s,rootMargin:i,threshold:"number"==typeof n?n:hl[n]});return r.forEach(t=>a.observe(t)),()=>a.disconnect()}(e.current,()=>(l(!0),r?void 0:()=>l(!1)),t)},[s,e,i,r,n]),a}export{un as A,nt as S,M as a,C as b,b as c,tt as d,T as e,E as f,c as g,w as h,f as i,r as j,k,ul as l,A as m,V as s,pl as u};
