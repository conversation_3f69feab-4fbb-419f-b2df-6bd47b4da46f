import{r as e,R as t}from"./router-D0oP4OMF.js";import{g as r}from"./vendor-BI3NJeJA.js";var n,i;const s=r(function(){if(i)return n;i=1;var e="undefined"!=typeof Element,t="function"==typeof Map,r="function"==typeof Set,s="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function o(n,i){if(n===i)return!0;if(n&&i&&"object"==typeof n&&"object"==typeof i){if(n.constructor!==i.constructor)return!1;var a,c,l,u;if(Array.isArray(n)){if((a=n.length)!=i.length)return!1;for(c=a;0!==c--;)if(!o(n[c],i[c]))return!1;return!0}if(t&&n instanceof Map&&i instanceof Map){if(n.size!==i.size)return!1;for(u=n.entries();!(c=u.next()).done;)if(!i.has(c.value[0]))return!1;for(u=n.entries();!(c=u.next()).done;)if(!o(c.value[1],i.get(c.value[0])))return!1;return!0}if(r&&n instanceof Set&&i instanceof Set){if(n.size!==i.size)return!1;for(u=n.entries();!(c=u.next()).done;)if(!i.has(c.value[0]))return!1;return!0}if(s&&ArrayBuffer.isView(n)&&ArrayBuffer.isView(i)){if((a=n.length)!=i.length)return!1;for(c=a;0!==c--;)if(n[c]!==i[c])return!1;return!0}if(n.constructor===RegExp)return n.source===i.source&&n.flags===i.flags;if(n.valueOf!==Object.prototype.valueOf&&"function"==typeof n.valueOf&&"function"==typeof i.valueOf)return n.valueOf()===i.valueOf();if(n.toString!==Object.prototype.toString&&"function"==typeof n.toString&&"function"==typeof i.toString)return n.toString()===i.toString();if((a=(l=Object.keys(n)).length)!==Object.keys(i).length)return!1;for(c=a;0!==c--;)if(!Object.prototype.hasOwnProperty.call(i,l[c]))return!1;if(e&&n instanceof Element)return!1;for(c=a;0!==c--;)if(("_owner"!==l[c]&&"__v"!==l[c]&&"__o"!==l[c]||!n.$$typeof)&&!o(n[l[c]],i[l[c]]))return!1;return!0}return n!=n&&i!=i}return n=function(e,t){try{return o(e,t)}catch(r){if((r.message||"").match(/stack|recursion/i))return!1;throw r}}}());var o,a;const c=r(a?o:(a=1,o=function(e,t,r,n,i,s,o,a){if(!e){var c;if(void 0===t)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[r,n,i,s,o,a],u=0;(c=new Error(t.replace(/%s/g,function(){return l[u++]}))).name="Invariant Violation"}throw c.framesToPop=1,c}}));var l,u;const p=r(u?l:(u=1,l=function(e,t,r,n){var i=r?r.call(n,e,t):void 0;if(void 0!==i)return!!i;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var s=Object.keys(e),o=Object.keys(t);if(s.length!==o.length)return!1;for(var a=Object.prototype.hasOwnProperty.bind(t),c=0;c<s.length;c++){var l=s[c];if(!a(l))return!1;var u=e[l],p=t[l];if(!1===(i=r?r.call(n,u,p,l):void 0)||void 0===i&&u!==p)return!1}return!0}));var f=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(f||{}),d={rel:["amphtml","canonical","alternate"]},h={type:["application/ld+json"]},m={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},y=Object.values(f),g={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},b=Object.entries(g).reduce((e,[t,r])=>(e[r]=t,e),{}),T="data-rh",A="defaultTitle",v="defer",O="encodeSpecialCharacters",w="onChangeClientState",C="titleTemplate",x="prioritizeSeoTags",j=(e,t)=>{for(let r=e.length-1;r>=0;r-=1){const n=e[r];if(Object.prototype.hasOwnProperty.call(n,t))return n[t]}return null},k=e=>{let t=j(e,"title");const r=j(e,C);if(Array.isArray(t)&&(t=t.join("")),r&&t)return r.replace(/%s/g,()=>t);const n=j(e,A);return t||n||void 0},S=e=>j(e,w)||(()=>{}),$=(e,t)=>t.filter(t=>void 0!==t[e]).map(t=>t[e]).reduce((e,t)=>({...e,...t}),{}),E=(e,t)=>t.filter(e=>void 0!==e.base).map(e=>e.base).reverse().reduce((t,r)=>{if(!t.length){const n=Object.keys(r);for(let i=0;i<n.length;i+=1){const s=n[i].toLowerCase();if(-1!==e.indexOf(s)&&r[s])return t.concat(r)}}return t},[]),M=(e,t,r)=>{const n={};return r.filter(t=>!!Array.isArray(t[e])||(void 0!==t[e]&&(t[e],console&&console.warn),!1)).map(t=>t[e]).reverse().reduce((e,r)=>{const i={};r.filter(e=>{let r;const s=Object.keys(e);for(let n=0;n<s.length;n+=1){const i=s[n],o=i.toLowerCase();-1===t.indexOf(o)||"rel"===r&&"canonical"===e[r].toLowerCase()||"rel"===o&&"stylesheet"===e[o].toLowerCase()||(r=o),-1===t.indexOf(i)||"innerHTML"!==i&&"cssText"!==i&&"itemprop"!==i||(r=i)}if(!r||!e[r])return!1;const o=e[r].toLowerCase();return n[r]||(n[r]={}),i[r]||(i[r]={}),!n[r][o]&&(i[r][o]=!0,!0)}).reverse().forEach(t=>e.push(t));const s=Object.keys(i);for(let t=0;t<s.length;t+=1){const e=s[t],r={...n[e],...i[e]};n[e]=r}return e},[]).reverse()},L=(e,t)=>{if(Array.isArray(e)&&e.length)for(let r=0;r<e.length;r+=1){if(e[r][t])return!0}return!1},P=e=>Array.isArray(e)?e.join(""):e,D=(e,t)=>Array.isArray(e)?e.reduce((e,r)=>(((e,t)=>{const r=Object.keys(e);for(let n=0;n<r.length;n+=1)if(t[r[n]]&&t[r[n]].includes(e[r[n]]))return!0;return!1})(r,t)?e.priority.push(r):e.default.push(r),e),{priority:[],default:[]}):{default:e,priority:[]},H=(e,t)=>({...e,[t]:void 0}),I=["noscript","script","style"],U=(e,t=!0)=>!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),N=e=>Object.keys(e).reduce((t,r)=>{const n=void 0!==e[r]?`${r}="${e[r]}"`:`${r}`;return t?`${t} ${n}`:n},""),R=(e,t={})=>Object.keys(e).reduce((t,r)=>(t[g[r]||r]=e[r],t),t),q=(e,r)=>r.map((r,n)=>{const i={key:n,[T]:!0};return Object.keys(r).forEach(e=>{const t=g[e]||e;if("innerHTML"===t||"cssText"===t){const e=r.innerHTML||r.cssText;i.dangerouslySetInnerHTML={__html:e}}else i[t]=r[e]}),t.createElement(e,i)}),z=(e,r,n=!0)=>{switch(e){case"title":return{toComponent:()=>((e,r,n)=>{const i=R(n,{key:r,[T]:!0});return[t.createElement("title",i,r)]})(0,r.title,r.titleAttributes),toString:()=>((e,t,r,n)=>{const i=N(r),s=P(t);return i?`<${e} ${T}="true" ${i}>${U(s,n)}</${e}>`:`<${e} ${T}="true">${U(s,n)}</${e}>`})(e,r.title,r.titleAttributes,n)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>R(r),toString:()=>N(r)};default:return{toComponent:()=>q(e,r),toString:()=>((e,t,r=!0)=>t.reduce((t,n)=>{const i=n,s=Object.keys(i).filter(e=>!("innerHTML"===e||"cssText"===e)).reduce((e,t)=>{const n=void 0===i[t]?t:`${t}="${U(i[t],r)}"`;return e?`${e} ${n}`:n},""),o=i.innerHTML||i.cssText||"",a=-1===I.indexOf(e);return`${t}<${e} ${T}="true" ${s}${a?"/>":`>${o}</${e}>`}`},""))(e,r,n)}}},B=e=>{const{baseTag:t,bodyAttributes:r,encode:n=!0,htmlAttributes:i,noscriptTags:s,styleTags:o,title:a="",titleAttributes:c,prioritizeSeoTags:l}=e;let{linkTags:u,metaTags:p,scriptTags:f}=e,y={toComponent:()=>{},toString:()=>""};return l&&({priorityMethods:y,linkTags:u,metaTags:p,scriptTags:f}=(({metaTags:e,linkTags:t,scriptTags:r,encode:n})=>{const i=D(e,m),s=D(t,d),o=D(r,h);return{priorityMethods:{toComponent:()=>[...q("meta",i.priority),...q("link",s.priority),...q("script",o.priority)],toString:()=>`${z("meta",i.priority,n)} ${z("link",s.priority,n)} ${z("script",o.priority,n)}`},metaTags:i.default,linkTags:s.default,scriptTags:o.default}})(e)),{priority:y,base:z("base",t,n),bodyAttributes:z("bodyAttributes",r,n),htmlAttributes:z("htmlAttributes",i,n),link:z("link",u,n),meta:z("meta",p,n),noscript:z("noscript",s,n),script:z("script",f,n),style:z("style",o,n),title:z("title",{title:a,titleAttributes:c},n)}},_=[],V=!("undefined"==typeof window||!window.document||!window.document.createElement),F=class{instances=[];canUseDOM=V;context;value={setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?_:this.instances,add:e=>{(this.canUseDOM?_:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?_:this.instances).indexOf(e);(this.canUseDOM?_:this.instances).splice(t,1)}}};constructor(e,t){this.context=e,this.canUseDOM=t||!1,t||(e.helmet=B({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},Y=t.createContext({}),K=class r extends e.Component{static canUseDOM=V;helmetData;constructor(e){super(e),this.helmetData=new F(this.props.context||{},r.canUseDOM)}render(){return t.createElement(Y.Provider,{value:this.helmetData.value},this.props.children)}},G=(e,t)=>{const r=document.head||document.querySelector("head"),n=r.querySelectorAll(`${e}[${T}]`),i=[].slice.call(n),s=[];let o;return t&&t.length&&t.forEach(t=>{const r=document.createElement(e);for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))if("innerHTML"===e)r.innerHTML=t.innerHTML;else if("cssText"===e)r.styleSheet?r.styleSheet.cssText=t.cssText:r.appendChild(document.createTextNode(t.cssText));else{const n=e,i=void 0===t[n]?"":t[n];r.setAttribute(e,i)}r.setAttribute(T,"true"),i.some((e,t)=>(o=t,r.isEqualNode(e)))?i.splice(o,1):s.push(r)}),i.forEach(e=>e.parentNode?.removeChild(e)),s.forEach(e=>r.appendChild(e)),{oldTags:i,newTags:s}},W=(e,t)=>{const r=document.getElementsByTagName(e)[0];if(!r)return;const n=r.getAttribute(T),i=n?n.split(","):[],s=[...i],o=Object.keys(t);for(const a of o){const e=t[a]||"";r.getAttribute(a)!==e&&r.setAttribute(a,e),-1===i.indexOf(a)&&i.push(a);const n=s.indexOf(a);-1!==n&&s.splice(n,1)}for(let a=s.length-1;a>=0;a-=1)r.removeAttribute(s[a]);i.length===s.length?r.removeAttribute(T):r.getAttribute(T)!==o.join(",")&&r.setAttribute(T,o.join(","))},J=(e,t)=>{const{baseTag:r,bodyAttributes:n,htmlAttributes:i,linkTags:s,metaTags:o,noscriptTags:a,onChangeClientState:c,scriptTags:l,styleTags:u,title:p,titleAttributes:f}=e;W("body",n),W("html",i),((e,t)=>{void 0!==e&&document.title!==e&&(document.title=P(e)),W("title",t)})(p,f);const d={baseTag:G("base",r),linkTags:G("link",s),metaTags:G("meta",o),noscriptTags:G("noscript",a),scriptTags:G("script",l),styleTags:G("style",u)},h={},m={};Object.keys(d).forEach(e=>{const{newTags:t,oldTags:r}=d[e];t.length&&(h[e]=t),r.length&&(m[e]=d[e].oldTags)}),t&&t(),c(e,h,m)},Q=null,X=e=>{Q&&cancelAnimationFrame(Q),e.defer?Q=requestAnimationFrame(()=>{J(e,()=>{Q=null})}):(J(e),Q=null)},Z=class extends e.Component{rendered=!1;shouldComponentUpdate(e){return!p(e,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:e}=this.props.context;e.remove(this),this.emitChange()}emitChange(){const{helmetInstances:e,setHelmet:t}=this.props.context;let r=null;const n=(i=e.get().map(e=>{const t={...e.props};return delete t.context,t}),{baseTag:E(["href"],i),bodyAttributes:$("bodyAttributes",i),defer:j(i,v),encode:j(i,O),htmlAttributes:$("htmlAttributes",i),linkTags:M("link",["rel","href"],i),metaTags:M("meta",["name","charset","http-equiv","property","itemprop"],i),noscriptTags:M("noscript",["innerHTML"],i),onChangeClientState:S(i),scriptTags:M("script",["src","innerHTML"],i),styleTags:M("style",["cssText"],i),title:k(i),titleAttributes:$("titleAttributes",i),prioritizeSeoTags:L(i,x)});var i;K.canUseDOM?X(n):B&&(r=B(n)),t(r)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:e}=this.props.context;e.add(this),this.emitChange()}render(){return this.init(),null}},ee=class extends e.Component{static defaultProps={defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1};shouldComponentUpdate(e){return!s(H(this.props,"helmetData"),H(e,"helmetData"))}mapNestedChildrenToProps(e,t){if(!t)return null;switch(e.type){case"script":case"noscript":return{innerHTML:t};case"style":return{cssText:t};default:throw new Error(`<${e.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(e,t,r,n){return{...t,[e.type]:[...t[e.type]||[],{...r,...this.mapNestedChildrenToProps(e,n)}]}}mapObjectTypeChildren(e,t,r,n){switch(e.type){case"title":return{...t,[e.type]:n,titleAttributes:{...r}};case"body":return{...t,bodyAttributes:{...r}};case"html":return{...t,htmlAttributes:{...r}};default:return{...t,[e.type]:{...r}}}}mapArrayTypeChildrenToProps(e,t){let r={...t};return Object.keys(e).forEach(t=>{r={...r,[t]:e[t]}}),r}warnOnInvalidChildren(e,t){return c(y.some(t=>e.type===t),"function"==typeof e.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${y.join(", ")} are allowed. Helmet does not support rendering <${e.type}> elements. Refer to our API for more information.`),c(!t||"string"==typeof t||Array.isArray(t)&&!t.some(e=>"string"!=typeof e),`Helmet expects a string as a child of <${e.type}>. Did you forget to wrap your children in braces? ( <${e.type}>{\`\`}</${e.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(e,r){let n={};return t.Children.forEach(e,e=>{if(!e||!e.props)return;const{children:t,...i}=e.props,s=Object.keys(i).reduce((e,t)=>(e[b[t]||t]=i[t],e),{});let{type:o}=e;switch("symbol"==typeof o?o=o.toString():this.warnOnInvalidChildren(e,t),o){case"Symbol(react.fragment)":r=this.mapChildrenToProps(t,r);break;case"link":case"meta":case"noscript":case"script":case"style":n=this.flattenArrayTypeChildren(e,n,s,t);break;default:r=this.mapObjectTypeChildren(e,r,s,t)}}),this.mapArrayTypeChildrenToProps(n,r)}render(){const{children:e,...r}=this.props;let n={...r},{helmetData:i}=r;if(e&&(n=this.mapChildrenToProps(e,n)),i&&!(i instanceof F)){i=new F(i.context,!0),delete n.helmetData}return i?t.createElement(Z,{...n,context:i.value}):t.createElement(Y.Consumer,null,e=>t.createElement(Z,{...n,context:e}))}};export{ee as H,K as a};
