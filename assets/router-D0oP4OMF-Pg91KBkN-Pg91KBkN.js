import{r as e,g as t}from"./vendor-BI3NJeJA.js";var n=e();const r=t(n);var a,o={};!function(){if(a)return o;a=1,Object.defineProperty(o,"__esModule",{value:!0}),o.parse=function(e,t){const n=new l,r=e.length;if(r<2)return n;const a=t?.decode||c;let o=0;do{const t=e.indexOf("=",o);if(-1===t)break;const i=e.indexOf(";",o),l=-1===i?r:i;if(t>l){o=e.lastIndexOf(";",t-1)+1;continue}const c=u(e,o,t),h=s(e,t,c),p=e.slice(c,h);if(void 0===n[p]){let r=u(e,t+1,l),o=s(e,l,r);const i=a(e.slice(r,o));n[p]=i}o=l+1}while(o<r);return n},o.serialize=function(a,o,l){const u=l?.encode||encodeURIComponent;if(!e.test(a))throw new TypeError(`argument name is invalid: ${a}`);const s=u(o);if(!t.test(s))throw new TypeError(`argument val is invalid: ${o}`);let c=a+"="+s;if(!l)return c;if(void 0!==l.maxAge){if(!Number.isInteger(l.maxAge))throw new TypeError(`option maxAge is invalid: ${l.maxAge}`);c+="; Max-Age="+l.maxAge}if(l.domain){if(!n.test(l.domain))throw new TypeError(`option domain is invalid: ${l.domain}`);c+="; Domain="+l.domain}if(l.path){if(!r.test(l.path))throw new TypeError(`option path is invalid: ${l.path}`);c+="; Path="+l.path}if(l.expires){if(!function(e){return"[object Date]"===i.call(e)}(l.expires)||!Number.isFinite(l.expires.valueOf()))throw new TypeError(`option expires is invalid: ${l.expires}`);c+="; Expires="+l.expires.toUTCString()}l.httpOnly&&(c+="; HttpOnly");l.secure&&(c+="; Secure");l.partitioned&&(c+="; Partitioned");if(l.priority){switch("string"==typeof l.priority?l.priority.toLowerCase():void 0){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${l.priority}`)}}if(l.sameSite){switch("string"==typeof l.sameSite?l.sameSite.toLowerCase():l.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${l.sameSite}`)}}return c};const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,r=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,l=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function u(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function s(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}}();var i="popstate";function l(e={}){return function(e,t,n,r={}){let{window:a=document.defaultView,v5Compat:o=!1}=r,l=a.history,s="POP",d=null,m=f();null==m&&(m=0,l.replaceState({...l.state,idx:m},""));function f(){return(l.state||{idx:null}).idx}function v(){s="POP";let e=f(),t=null==e?null:e-m;m=e,d&&d({action:s,location:b.location,delta:t})}function y(e,t){s="PUSH";let n=h(b.location,e,t);m=f()+1;let r=c(n,m),i=b.createHref(n);try{l.pushState(r,"",i)}catch(u){if(u instanceof DOMException&&"DataCloneError"===u.name)throw u;a.location.assign(i)}o&&d&&d({action:s,location:b.location,delta:1})}function g(e,t){s="REPLACE";let n=h(b.location,e,t);m=f();let r=c(n,m),a=b.createHref(n);l.replaceState(r,"",a),o&&d&&d({action:s,location:b.location,delta:0})}function w(e){return function(e,t=!1){let n="http://localhost";"undefined"!=typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href);u(n,"No window.location.(origin|href) available to create URL");let r="string"==typeof e?e:p(e);r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r);return new URL(r,n)}(e)}let b={get action(){return s},get location(){return e(a,l)},listen(e){if(d)throw new Error("A history only accepts one active listener");return a.addEventListener(i,v),d=e,()=>{a.removeEventListener(i,v),d=null}},createHref:e=>t(a,e),createURL:w,encodeLocation(e){let t=w(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:y,replace:g,go:e=>l.go(e)};return b}(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return h("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:p(t)},0,e)}function u(e,t){if(!1===e||null==e)throw new Error(t)}function s(e,t){if(!e)try{throw new Error(t)}catch(n){}}function c(e,t){return{usr:e.state,key:e.key,idx:t}}function h(e,t,n=null,r){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?d(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function p({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function d(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function m(e,t,n="/"){return function(e,t,n,r){let a="string"==typeof t?d(t):t,o=L(a.pathname||"/",n);if(null==o)return null;let i=f(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(i);let l=null;for(let u=0;null==l&&u<i.length;++u){let e=k(o);l=S(i[u],e,r)}return l}(e,t,n,!1)}function f(e,t=[],n=[],r=""){let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(u(i.relativePath.startsWith(r),`Absolute route path "${i.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),i.relativePath=i.relativePath.slice(r.length));let l=N([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(u(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${l}".`),f(e.children,t,s,l)),(null!=e.path||e.index)&&t.push({path:l,score:R(l,e.index),routesMeta:s})};return e.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of v(e.path))a(e,t,n);else a(e,t)}),t}function v(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=v(r.join("/")),l=[];return l.push(...i.map(e=>""===e?o:[o,e].join("/"))),a&&l.push(...i),l.map(t=>e.startsWith("/")&&""===t?"/":t)}var y=/^:[\w-]+$/,g=3,w=2,b=1,x=10,E=-2,C=e=>"*"===e;function R(e,t){let n=e.split("/"),r=n.length;return n.some(C)&&(r+=E),t&&(r+=w),n.filter(e=>!C(e)).reduce((e,t)=>e+(y.test(t)?g:""===t?b:x),r)}function S(e,t,n=!1){let{routesMeta:r}=e,a={},o="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],u=l===r.length-1,s="/"===o?t:t.slice(o.length)||"/",c=$({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),h=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=$({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:N([o,c.pathname]),pathnameBase:O(N([o,c.pathnameBase])),route:h}),"/"!==c.pathnameBase&&(o=N([o,c.pathnameBase]))}return i}function $(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t=!1,n=!0){s("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:r.reduce((e,{paramName:t,isOptional:n},r)=>{if("*"===t){let e=l[r]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const a=l[r];return e[t]=n&&!a?void 0:(a||"").replace(/%2F/g,"/"),e},{}),pathname:o,pathnameBase:i,pattern:e}}function k(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return s(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function L(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function P(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function T(e){let t=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function A(e,t,n,r=!1){let a;"string"==typeof e?a=d(e):(a={...e},u(!a.pathname||!a.pathname.includes("?"),P("?","pathname","search",a)),u(!a.pathname||!a.pathname.includes("#"),P("#","pathname","hash",a)),u(!a.search||!a.search.includes("#"),P("#","search","hash",a)));let o,i=""===e||""===a.pathname,l=i?"/":a.pathname;if(null==l)o=n;else{let e=t.length-1;if(!r&&l.startsWith("..")){let t=l.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t="/"){let{pathname:n,search:r="",hash:a=""}="string"==typeof e?d(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:M(r),hash:F(a)}}(a,o),c=l&&"/"!==l&&l.endsWith("/"),h=(i||"."===l)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!h||(s.pathname+="/"),s}var N=e=>e.join("/").replace(/\/\/+/g,"/"),O=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),M=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",F=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var D=["POST","PUT","PATCH","DELETE"];new Set(D);var U=["GET",...D];new Set(U);var W=n.createContext(null);W.displayName="DataRouter";var j=n.createContext(null);j.displayName="DataRouterState";var B=n.createContext({isTransitioning:!1});B.displayName="ViewTransition",n.createContext(new Map).displayName="Fetchers",n.createContext(null).displayName="Await";var z=n.createContext(null);z.displayName="Navigation";var I=n.createContext(null);I.displayName="Location";var _=n.createContext({outlet:null,matches:[],isDataRoute:!1});_.displayName="Route";var H=n.createContext(null);function Y(){return null!=n.useContext(I)}function J(){return u(Y(),"useLocation() may be used only in the context of a <Router> component."),n.useContext(I).location}H.displayName="RouteError";var K="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function V(e){n.useContext(z).static||n.useLayoutEffect(e)}function q(){let{isDataRoute:e}=n.useContext(_);return e?function(){let{router:e}=function(e){let t=n.useContext(W);return u(t,ne(e)),t}("useNavigate"),t=re("useNavigate"),r=n.useRef(!1);return V(()=>{r.current=!0}),n.useCallback(async(n,a={})=>{s(r.current,K),r.current&&("number"==typeof n?e.navigate(n):await e.navigate(n,{fromRouteId:t,...a}))},[e,t])}():function(){u(Y(),"useNavigate() may be used only in the context of a <Router> component.");let e=n.useContext(W),{basename:t,navigator:r}=n.useContext(z),{matches:a}=n.useContext(_),{pathname:o}=J(),i=JSON.stringify(T(a)),l=n.useRef(!1);return V(()=>{l.current=!0}),n.useCallback((n,a={})=>{if(s(l.current,K),!l.current)return;if("number"==typeof n)return void r.go(n);let u=A(n,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(u.pathname="/"===u.pathname?t:N([t,u.pathname])),(a.replace?r.replace:r.push)(u,a.state,a)},[t,r,i,o,e])}()}function G(e,{relative:t}={}){let{matches:r}=n.useContext(_),{pathname:a}=J(),o=JSON.stringify(T(r));return n.useMemo(()=>A(e,JSON.parse(o),a,"path"===t),[e,o,a,t])}function X(e,t,r,a){u(Y(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=n.useContext(z),{matches:i}=n.useContext(_),l=i[i.length-1],c=l?l.params:{},h=l?l.pathname:"/",p=l?l.pathnameBase:"/",f=l&&l.route;{let e=f&&f.path||"";oe(h,!f||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${h}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let v,y=J();if(t){let e="string"==typeof t?d(t):t;u("/"===p||e.pathname?.startsWith(p),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${p}" but pathname "${e.pathname}" was given in the \`location\` prop.`),v=e}else v=y;let g=v.pathname||"/",w=g;if("/"!==p){let e=p.replace(/^\//,"").split("/");w="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=m(e,{pathname:w});s(f||null!=b,`No routes matched location "${v.pathname}${v.search}${v.hash}" `),s(null==b||void 0!==b[b.length-1].route.element||void 0!==b[b.length-1].route.Component||void 0!==b[b.length-1].route.lazy,`Matched leaf route at location "${v.pathname}${v.search}${v.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let x=function(e,t=[],r=null){if(null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let a=e,o=r?.errors;if(null!=o){let e=a.findIndex(e=>e.route.id&&void 0!==o?.[e.route.id]);u(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),a=a.slice(0,Math.min(a.length,e+1))}let i=!1,l=-1;if(r)for(let n=0;n<a.length;n++){let e=a[n];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=n),e.route.id){let{loaderData:t,errors:n}=r,o=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!n||void 0===n[e.route.id]);if(e.route.lazy||o){i=!0,a=l>=0?a.slice(0,l+1):[a[0]];break}}}return a.reduceRight((e,u,s)=>{let c,h=!1,p=null,d=null;r&&(c=o&&u.route.id?o[u.route.id]:void 0,p=u.route.errorElement||Z,i&&(l<0&&0===s?(oe("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,d=null):l===s&&(h=!0,d=u.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,s+1)),f=()=>{let t;return t=c?p:h?d:u.route.Component?n.createElement(u.route.Component,null):u.route.element?u.route.element:e,n.createElement(te,{match:u,routeContext:{outlet:e,matches:m,isDataRoute:null!=r},children:t})};return r&&(u.route.ErrorBoundary||u.route.errorElement||0===s)?n.createElement(ee,{location:r.location,revalidation:r.revalidation,component:p,error:c,children:f(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):f()},null)}(b&&b.map(e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:N([p,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?p:N([p,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,r,a);return t&&x?n.createElement(I.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...v},navigationType:"POP"}},x):x}function Q(){let e=function(){let e=n.useContext(H),t=function(e){let t=n.useContext(j);return u(t,ne(e)),t}("useRouteError"),r=re("useRouteError");if(void 0!==e)return e;return t.errors?.[r]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:a},i={padding:"2px 4px",backgroundColor:a},l=null;return l=n.createElement(n.Fragment,null,n.createElement("p",null,"💿 Hey developer 👋"),n.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",n.createElement("code",{style:i},"ErrorBoundary")," or"," ",n.createElement("code",{style:i},"errorElement")," prop on your route.")),n.createElement(n.Fragment,null,n.createElement("h2",null,"Unexpected Application Error!"),n.createElement("h3",{style:{fontStyle:"italic"}},t),r?n.createElement("pre",{style:o},r):null,l)}n.createContext(null);var Z=n.createElement(Q,null),ee=class extends n.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){}render(){return void 0!==this.state.error?n.createElement(_.Provider,{value:this.props.routeContext},n.createElement(H.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function te({routeContext:e,match:t,children:r}){let a=n.useContext(W);return a&&a.static&&a.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=t.route.id),n.createElement(_.Provider,{value:e},r)}function ne(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function re(e){let t=function(e){let t=n.useContext(_);return u(t,ne(e)),t}(e),r=t.matches[t.matches.length-1];return u(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}var ae={};function oe(e,t,n){t||ae[e]||(ae[e]=!0,s(!1,n))}function ie(e){u(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function le({basename:e="/",children:t=null,location:r,navigationType:a="POP",navigator:o,static:i=!1}){u(!Y(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=e.replace(/^\/*/,"/"),c=n.useMemo(()=>({basename:l,navigator:o,static:i,future:{}}),[l,o,i]);"string"==typeof r&&(r=d(r));let{pathname:h="/",search:p="",hash:m="",state:f=null,key:v="default"}=r,y=n.useMemo(()=>{let e=L(h,l);return null==e?null:{location:{pathname:e,search:p,hash:m,state:f,key:v},navigationType:a}},[l,h,p,m,f,v,a]);return s(null!=y,`<Router basename="${l}"> is not able to match the URL "${h}${p}${m}" because it does not start with the basename, so the <Router> won't render anything.`),null==y?null:n.createElement(z.Provider,{value:c},n.createElement(I.Provider,{children:t,value:y}))}function ue({children:e,location:t}){return X(se(e),t)}function se(e,t=[]){let r=[];return n.Children.forEach(e,(e,a)=>{if(!n.isValidElement(e))return;let o=[...t,a];if(e.type===n.Fragment)return void r.push.apply(r,se(e.props.children,o));u(e.type===ie,`[${"string"==typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),u(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=se(e.props.children,o)),r.push(i)}),r}n.memo(function({routes:e,future:t,state:n}){return X(e,void 0,n,t)});var ce="get",he="application/x-www-form-urlencoded";function pe(e){return null!=e&&"string"==typeof e.tagName}var de=null;var me=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function fe(e){return null==e||me.has(e)?e:(s(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${he}"`),null)}function ve(e,t){let n,r,a,o,i;if(pe(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?L(i,t):null,n=e.getAttribute("method")||ce,a=fe(e.getAttribute("enctype"))||he,o=new FormData(e)}else if(function(e){return pe(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return pe(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?L(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||ce,a=fe(e.getAttribute("formenctype"))||fe(i.getAttribute("enctype"))||he,o=new FormData(i,e),!function(){if(null===de)try{new FormData(document.createElement("form"),0),de=!1}catch(e){de=!0}return de}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(pe(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ce,r=null,a=he,i=e}var l;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}function ye(e,t){if(!1===e||null==e)throw new Error(t)}function ge(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function we(e,t,n){return function(e,t){let n=new Set;return new Set(t),e.reduce((e,t)=>{let r=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(t));return n.has(r)||(n.add(r),e.push({key:r,link:t})),e},[])}((await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}(r,n);return e.links?e.links():[]}return[]}))).flat(1).filter(ge).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"}))}function be(e,t,n,r,a,o){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===o?t.filter((e,t)=>i(e,t)||l(e,t)):"data"===o?t.filter((t,o)=>{let u=r.routes[t.route.id];if(!u||!u.hasLoader)return!1;if(i(t,o)||l(t,o))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0}):[]}function xe(e,t,{includeHydrateFallback:n}={}){return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a}).flat(1),[...new Set(r)];var r}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Ee(){let e=n.useContext(W);return ye(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}var Ce=n.createContext(void 0);function Re(){let e=n.useContext(Ce);return ye(e,"You must render this element inside a <HydratedRouter> element"),e}function Se(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function $e({page:e,...t}){let{router:r}=Ee(),a=n.useMemo(()=>m(r.routes,e,r.basename),[r.routes,e,r.basename]);return a?n.createElement(ke,{page:e,matches:a,...t}):null}function ke({page:e,matches:t,...r}){let a=J(),{manifest:o,routeModules:i}=Re(),{basename:l}=Ee(),{loaderData:u,matches:s}=function(){let e=n.useContext(j);return ye(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}(),c=n.useMemo(()=>be(e,t,s,o,a,"data"),[e,t,s,o,a]),h=n.useMemo(()=>be(e,t,s,o,a,"assets"),[e,t,s,o,a]),p=n.useMemo(()=>{if(e===a.pathname+a.search+a.hash)return[];let n=new Set,r=!1;if(t.forEach(e=>{let t=o.routes[e.route.id];t&&t.hasLoader&&(!c.some(t=>t.route.id===e.route.id)&&e.route.id in u&&i[e.route.id]?.shouldRevalidate||t.hasClientLoader?r=!0:n.add(e.route.id))}),0===n.size)return[];let s=function(e,t){let n="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===L(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}(e,l);return r&&n.size>0&&s.searchParams.set("_routes",t.filter(e=>n.has(e.route.id)).map(e=>e.route.id).join(",")),[s.pathname+s.search]},[l,u,a,o,c,t,e,i]),d=n.useMemo(()=>xe(h,o),[h,o]),m=function(e){let{manifest:t,routeModules:r}=Re(),[a,o]=n.useState([]);return n.useEffect(()=>{let n=!1;return we(e,t,r).then(e=>{n||o(e)}),()=>{n=!0}},[e,t,r]),a}(h);return n.createElement(n.Fragment,null,p.map(e=>n.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r})),d.map(e=>n.createElement("link",{key:e,rel:"modulepreload",href:e,...r})),m.map(({key:e,link:t})=>n.createElement("link",{key:e,...t})))}function Le(...e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}Ce.displayName="FrameworkContext";var Pe="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{Pe&&(window.__reactRouterVersion="7.6.3")}catch(De){}function Te({basename:e,children:t,window:r}){let a=n.useRef();null==a.current&&(a.current=l({window:r,v5Compat:!0}));let o=a.current,[i,u]=n.useState({action:o.action,location:o.location}),s=n.useCallback(e=>{n.startTransition(()=>u(e))},[u]);return n.useLayoutEffect(()=>o.listen(s),[o,s]),n.createElement(le,{basename:e,children:t,location:i.location,navigationType:i.action,navigator:o})}var Ae=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ne=n.forwardRef(function({onClick:e,discover:t="render",prefetch:r="none",relative:a,reloadDocument:o,replace:i,state:l,target:c,to:h,preventScrollReset:d,viewTransition:m,...f},v){let y,{basename:g}=n.useContext(z),w="string"==typeof h&&Ae.test(h),b=!1;if("string"==typeof h&&w&&(y=h,Pe))try{let e=new URL(window.location.href),t=h.startsWith("//")?new URL(e.protocol+h):new URL(h),n=L(t.pathname,g);t.origin===e.origin&&null!=n?h=n+t.search+t.hash:b=!0}catch(De){s(!1,`<Link to="${h}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let x=function(e,{relative:t}={}){u(Y(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:a}=n.useContext(z),{hash:o,pathname:i,search:l}=G(e,{relative:t}),s=i;return"/"!==r&&(s="/"===i?r:N([r,i])),a.createHref({pathname:s,search:l,hash:o})}(h,{relative:a}),[E,C,R]=function(e,t){let r=n.useContext(Ce),[a,o]=n.useState(!1),[i,l]=n.useState(!1),{onFocus:u,onBlur:s,onMouseEnter:c,onMouseLeave:h,onTouchStart:p}=t,d=n.useRef(null);n.useEffect(()=>{if("render"===e&&l(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{l(e.isIntersecting)})},{threshold:.5});return d.current&&e.observe(d.current),()=>{e.disconnect()}}},[e]),n.useEffect(()=>{if(a){let e=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(e)}}},[a]);let m=()=>{o(!0)},f=()=>{o(!1),l(!1)};return r?"intent"!==e?[i,d,{}]:[i,d,{onFocus:Se(u,m),onBlur:Se(s,f),onMouseEnter:Se(c,m),onMouseLeave:Se(h,f),onTouchStart:Se(p,m)}]:[!1,d,{}]}(r,f),S=function(e,{target:t,replace:r,state:a,preventScrollReset:o,relative:i,viewTransition:l}={}){let u=q(),s=J(),c=G(e,{relative:i});return n.useCallback(n=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(n,t)){n.preventDefault();let t=void 0!==r?r:p(s)===p(c);u(e,{replace:t,state:a,preventScrollReset:o,relative:i,viewTransition:l})}},[s,u,c,r,a,t,e,o,i,l])}(h,{replace:i,state:l,target:c,preventScrollReset:d,relative:a,viewTransition:m});let $=n.createElement("a",{...f,...R,href:y||x,onClick:b||o?e:function(t){e&&e(t),t.defaultPrevented||S(t)},ref:Le(v,C),target:c,"data-discover":w||"render"!==t?void 0:"true"});return E&&!w?n.createElement(n.Fragment,null,$,n.createElement($e,{page:x})):$});function Oe(e){let t=n.useContext(W);return u(t,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(e)),t}Ne.displayName="Link",n.forwardRef(function({"aria-current":e="page",caseSensitive:t=!1,className:r="",end:a=!1,style:o,to:i,viewTransition:l,children:s,...c},h){let p=G(i,{relative:c.relative}),d=J(),m=n.useContext(j),{navigator:f,basename:v}=n.useContext(z),y=null!=m&&function(e,t={}){let r=n.useContext(B);u(null!=r,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=Oe("useViewTransitionState"),o=G(e,{relative:t.relative});if(!r.isTransitioning)return!1;let i=L(r.currentLocation.pathname,a)||r.currentLocation.pathname,l=L(r.nextLocation.pathname,a)||r.nextLocation.pathname;return null!=$(o.pathname,l)||null!=$(o.pathname,i)}(p)&&!0===l,g=f.encodeLocation?f.encodeLocation(p).pathname:p.pathname,w=d.pathname,b=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;t||(w=w.toLowerCase(),b=b?b.toLowerCase():null,g=g.toLowerCase()),b&&v&&(b=L(b,v)||b);const x="/"!==g&&g.endsWith("/")?g.length-1:g.length;let E,C=w===g||!a&&w.startsWith(g)&&"/"===w.charAt(x),R=null!=b&&(b===g||!a&&b.startsWith(g)&&"/"===b.charAt(g.length)),S={isActive:C,isPending:R,isTransitioning:y},k=C?e:void 0;E="function"==typeof r?r(S):[r,C?"active":null,R?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let P="function"==typeof o?o(S):o;return n.createElement(Ne,{...c,"aria-current":k,className:E,ref:h,style:P,to:i,viewTransition:l},"function"==typeof s?s(S):s)}).displayName="NavLink",n.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:a,replace:o,state:i,method:l=ce,action:s,onSubmit:c,relative:h,preventScrollReset:d,viewTransition:m,...f},v)=>{let y=function(){let{router:e}=Oe("useSubmit"),{basename:t}=n.useContext(z),r=re("useRouteId");return n.useCallback(async(n,a={})=>{let{action:o,method:i,encType:l,formData:u,body:s}=ve(n,t);if(!1===a.navigate){let t=a.fetcherKey||Fe();await e.fetch(t,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}(),g=function(e,{relative:t}={}){let{basename:r}=n.useContext(z),a=n.useContext(_);u(a,"useFormAction must be used inside a RouteContext");let[o]=a.matches.slice(-1),i={...G(e||".",{relative:t})},l=J();if(null==e){i.search=l.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();i.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==r&&(i.pathname="/"===i.pathname?r:N([r,i.pathname]));return p(i)}(s,{relative:h}),w="get"===l.toLowerCase()?"get":"post",b="string"==typeof s&&Ae.test(s);return n.createElement("form",{ref:v,method:w,action:g,onSubmit:a?c:e=>{if(c&&c(e),e.defaultPrevented)return;e.preventDefault();let n=e.nativeEvent.submitter,a=n?.getAttribute("formmethod")||l;y(n||e.currentTarget,{fetcherKey:t,method:a,navigate:r,replace:o,state:i,relative:h,preventScrollReset:d,viewTransition:m})},...f,"data-discover":b||"render"!==e?void 0:"true"})}).displayName="Form";var Me=0,Fe=()=>`__${String(++Me)}__`;export{Te as B,Ne as L,r as R,q as a,ue as b,ie as c,n as r,J as u};
